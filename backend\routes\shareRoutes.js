const express = require('express');
const router = express.Router();
const shareService = require('../services/shareService');
const { ensureAuthenticated } = require('../auth/googleAuth');

/**
 * @route POST /api/share
 * @desc Create a new share for a prototype
 * @access Private
 */
router.post('/', ensureAuthenticated, async (req, res) => {
  try {
    const { prototypeId, sharedWithEmail, isPublic, accessLevel, expiresAt } = req.body;

    if (!prototypeId) {
      return res.status(400).json({ error: 'Prototype ID is required' });
    }

    // For private shares, email is required
    if (!isPublic && !sharedWithEmail) {
      return res.status(400).json({ error: 'Email is required for private shares' });
    }

    // No special handling for dummy IDs anymore - use the real implementation

    // Log user object for debugging
    console.log('[Share Debug] User object:', req.user);

    // Use dbId instead of _id
    const ownerId = req.user.dbId;

    if (!ownerId) {
      console.error('[Share Debug] Owner ID is undefined or null');
      return res.status(400).json({ error: 'User ID is missing' });
    }

    console.log(`[Share Debug] Creating share for prototype: ${prototypeId}, owner: ${ownerId}`);
    console.log(`[Share Debug] Share request body:`, req.body);

    const result = await shareService.createShare({
      prototypeId,
      ownerId,
      sharedWithEmail,
      isPublic,
      accessLevel,
      expiresAt
    });

    res.status(201).json(result);
  } catch (error) {
    console.error('Error creating share:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route GET /api/share/prototype/:prototypeId
 * @desc Get all shares for a prototype
 * @access Private
 */
router.get('/prototype/:prototypeId', ensureAuthenticated, async (req, res) => {
  try {
    const { prototypeId } = req.params;

    // No special handling for dummy IDs anymore

    // Log user object for debugging
    console.log('[Share Debug] User object:', req.user);

    // Use dbId instead of _id
    const ownerId = req.user.dbId;

    if (!ownerId) {
      console.error('[Share Debug] Owner ID is undefined or null');
      return res.status(400).json({ error: 'User ID is missing' });
    }

    console.log(`[Share Debug] Getting shares for prototype: ${prototypeId}, owner: ${ownerId}`);

    const shares = await shareService.getSharesForPrototype(prototypeId, ownerId);

    res.json(shares);
  } catch (error) {
    console.error('Error getting shares:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route GET /api/share/shared-with-me
 * @desc Get all prototypes shared with the current user
 * @access Private
 */
router.get('/shared-with-me', ensureAuthenticated, async (req, res) => {
  try {
    const sharedPrototypes = await shareService.getPrototypesSharedWithUser(req.user.email);

    res.json(sharedPrototypes);
  } catch (error) {
    console.error('Error getting shared prototypes:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route PUT /api/share/:shareId
 * @desc Update a share
 * @access Private
 */
router.put('/:shareId', ensureAuthenticated, async (req, res) => {
  try {
    const { shareId } = req.params;
    const { accessLevel, isActive, expiresAt } = req.body;

    // Use dbId instead of _id
    const ownerId = req.user.dbId;

    if (!ownerId) {
      console.error('[Share Debug] Owner ID is undefined or null');
      return res.status(400).json({ error: 'User ID is missing' });
    }

    const updatedShare = await shareService.updateShare(shareId, ownerId, {
      accessLevel,
      isActive,
      expiresAt
    });

    res.json(updatedShare);
  } catch (error) {
    console.error('Error updating share:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route DELETE /api/share/:shareId
 * @desc Delete a share
 * @access Private
 */
router.delete('/:shareId', ensureAuthenticated, async (req, res) => {
  try {
    const { shareId } = req.params;

    // Use dbId instead of _id
    const ownerId = req.user.dbId;

    if (!ownerId) {
      console.error('[Share Debug] Owner ID is undefined or null');
      return res.status(400).json({ error: 'User ID is missing' });
    }

    const deleted = await shareService.deleteShare(shareId, ownerId);

    if (deleted) {
      res.json({ success: true });
    } else {
      res.status(404).json({ error: 'Share not found' });
    }
  } catch (error) {
    console.error('Error deleting share:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route GET /api/share/access/:accessToken
 * @desc Get a prototype by share token
 * @access Public
 */
router.get('/access/:accessToken', async (req, res) => {
  try {
    const { accessToken } = req.params;

    const result = await shareService.getPrototypeByShareToken(accessToken);

    res.json(result);
  } catch (error) {
    console.error('Error getting shared prototype:', error);
    res.status(404).json({ error: error.message });
  }
});

/**
 * @route POST /api/share/validate-access
 * @desc Validate if a user has access to a prototype
 * @access Public
 */
router.post('/validate-access', async (req, res) => {
  try {
    const { prototypeId, email, accessToken } = req.body;

    if (!prototypeId) {
      return res.status(400).json({ error: 'Prototype ID is required' });
    }

    const accessInfo = await shareService.validateAccess(prototypeId, email, accessToken);

    res.json(accessInfo);
  } catch (error) {
    console.error('Error validating access:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
