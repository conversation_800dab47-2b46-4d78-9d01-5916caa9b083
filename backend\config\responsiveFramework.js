/**
 * Responsive CSS Framework for JustPrototype
 * 
 * This module provides a comprehensive responsive design framework that enhances
 * all generated HTML pages with mobile-first responsive design, modern UI/UX patterns,
 * and accessibility features.
 */

/**
 * Core responsive CSS framework with mobile-first design principles
 */
const RESPONSIVE_CSS_FRAMEWORK = `
/* ===== RESPONSIVE FRAMEWORK - MOBILE FIRST ===== */

/* CSS Custom Properties for Responsive Design */
:root {
  /* Responsive Breakpoints */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
  
  /* Responsive Typography Scale */
  --text-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
  --text-sm: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
  --text-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
  --text-lg: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);
  --text-xl: clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem);
  --text-2xl: clamp(1.5rem, 1.3rem + 1vw, 2rem);
  --text-3xl: clamp(1.875rem, 1.6rem + 1.375vw, 2.5rem);
  --text-4xl: clamp(2.25rem, 1.9rem + 1.75vw, 3rem);
  --text-5xl: clamp(3rem, 2.5rem + 2.5vw, 4rem);
  
  /* Responsive Spacing Scale */
  --space-xs: clamp(0.25rem, 0.2rem + 0.25vw, 0.375rem);
  --space-sm: clamp(0.5rem, 0.4rem + 0.5vw, 0.75rem);
  --space-md: clamp(1rem, 0.8rem + 1vw, 1.5rem);
  --space-lg: clamp(1.5rem, 1.2rem + 1.5vw, 2.25rem);
  --space-xl: clamp(2rem, 1.6rem + 2vw, 3rem);
  --space-2xl: clamp(3rem, 2.4rem + 3vw, 4.5rem);
  --space-3xl: clamp(4rem, 3.2rem + 4vw, 6rem);
  
  /* Touch Target Sizes */
  --touch-target-min: 44px;
  --touch-target-comfortable: 48px;
  --touch-target-large: 56px;
  
  /* Container Sizes */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;
  
  /* Modern Color System */
  --color-primary: #3b82f6;
  --color-primary-light: #60a5fa;
  --color-primary-dark: #1d4ed8;
  --color-secondary: #10b981;
  --color-accent: #f59e0b;
  --color-neutral-50: #f9fafb;
  --color-neutral-100: #f3f4f6;
  --color-neutral-200: #e5e7eb;
  --color-neutral-300: #d1d5db;
  --color-neutral-400: #9ca3af;
  --color-neutral-500: #6b7280;
  --color-neutral-600: #4b5563;
  --color-neutral-700: #374151;
  --color-neutral-800: #1f2937;
  --color-neutral-900: #111827;
  
  /* Animation Variables */
  --transition-fast: 150ms ease-out;
  --transition-normal: 250ms ease-out;
  --transition-slow: 350ms ease-out;
  --animation-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* ===== RESPONSIVE BASE STYLES ===== */

/* Enhanced Box Sizing */
*, *::before, *::after {
  box-sizing: border-box;
}

/* Mobile-First Body Styles */
body {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: var(--text-base);
  line-height: 1.6;
  color: var(--color-neutral-800);
  background-color: var(--color-neutral-50);
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Responsive Images */
img, picture, video, canvas, svg {
  display: block;
  max-width: 100%;
  height: auto;
}

/* ===== RESPONSIVE TYPOGRAPHY ===== */

h1, h2, h3, h4, h5, h6 {
  margin: 0 0 var(--space-md) 0;
  font-weight: 600;
  line-height: 1.2;
  color: var(--color-neutral-900);
}

h1 { font-size: var(--text-4xl); }
h2 { font-size: var(--text-3xl); }
h3 { font-size: var(--text-2xl); }
h4 { font-size: var(--text-xl); }
h5 { font-size: var(--text-lg); }
h6 { font-size: var(--text-base); }

p {
  margin: 0 0 var(--space-md) 0;
  font-size: var(--text-base);
}

/* ===== RESPONSIVE LAYOUT UTILITIES ===== */

/* Container System */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--space-md);
  padding-right: var(--space-md);
}

@media (min-width: 640px) {
  .container {
    max-width: var(--container-sm);
    padding-left: var(--space-lg);
    padding-right: var(--space-lg);
  }
}

@media (min-width: 768px) {
  .container {
    max-width: var(--container-md);
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: var(--container-lg);
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: var(--container-xl);
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: var(--container-2xl);
  }
}

/* Responsive Grid System */
.grid-responsive {
  display: grid;
  gap: var(--space-md);
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .grid-responsive {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) {
  .grid-responsive {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid-responsive {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Responsive Flexbox Utilities */
.flex-responsive {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

@media (min-width: 768px) {
  .flex-responsive {
    flex-direction: row;
  }
}
`;

/**
 * Touch-friendly component styles for mobile devices
 */
const TOUCH_FRIENDLY_COMPONENTS = `
/* ===== TOUCH-FRIENDLY COMPONENTS ===== */

/* Enhanced Button Styles */
button, .btn, [role="button"] {
  min-height: var(--touch-target-min);
  min-width: var(--touch-target-min);
  padding: var(--space-sm) var(--space-md);
  border: none;
  border-radius: 8px;
  font-size: var(--text-base);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-xs);
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

/* Button Variants */
.btn-primary {
  background-color: var(--color-primary);
  color: white;
}

.btn-primary:hover, .btn-primary:focus {
  background-color: var(--color-primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-secondary {
  background-color: var(--color-neutral-100);
  color: var(--color-neutral-700);
  border: 1px solid var(--color-neutral-200);
}

.btn-secondary:hover, .btn-secondary:focus {
  background-color: var(--color-neutral-200);
  border-color: var(--color-neutral-300);
}

/* Touch Feedback */
button:active, .btn:active {
  transform: translateY(0);
  transition: transform 100ms ease-out;
}

/* Enhanced Form Controls */
input, textarea, select {
  min-height: var(--touch-target-min);
  padding: var(--space-sm) var(--space-md);
  border: 2px solid var(--color-neutral-200);
  border-radius: 8px;
  font-size: var(--text-base);
  font-family: inherit;
  transition: all var(--transition-normal);
  width: 100%;
  background-color: white;
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Enhanced Links */
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-normal);
  min-height: var(--touch-target-min);
  display: inline-flex;
  align-items: center;
  padding: var(--space-xs) 0;
}

a:hover, a:focus {
  color: var(--color-primary-dark);
  text-decoration: underline;
}

/* Card Components */
.card {
  background: white;
  border-radius: 12px;
  padding: var(--space-lg);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all var(--transition-normal);
  border: 1px solid var(--color-neutral-100);
}

.card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* Navigation Components */
nav {
  padding: var(--space-md);
}

nav ul {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

@media (min-width: 768px) {
  nav ul {
    flex-direction: row;
    gap: var(--space-lg);
  }
}

nav a {
  padding: var(--space-sm) var(--space-md);
  border-radius: 6px;
  transition: all var(--transition-normal);
  min-height: var(--touch-target-min);
  display: flex;
  align-items: center;
}

nav a:hover, nav a:focus {
  background-color: var(--color-neutral-100);
  text-decoration: none;
}
`;

/**
 * Mobile-specific enhancements and gestures
 */
const MOBILE_ENHANCEMENTS = `
/* ===== MOBILE-SPECIFIC ENHANCEMENTS ===== */

/* Touch Optimization */
html {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-text-size-adjust: 100%;
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Mobile Navigation */
.mobile-nav-toggle {
  display: block;
  background: none;
  border: none;
  font-size: 1.5rem;
  padding: var(--space-sm);
  cursor: pointer;
}

@media (min-width: 768px) {
  .mobile-nav-toggle {
    display: none;
  }
}

.mobile-nav {
  position: fixed;
  top: 0;
  left: -100%;
  width: 280px;
  height: 100vh;
  background: white;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  transition: left var(--transition-normal);
  z-index: 1000;
  padding: var(--space-lg);
  overflow-y: auto;
}

.mobile-nav.open {
  left: 0;
}

.mobile-nav-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
  z-index: 999;
}

.mobile-nav-overlay.open {
  opacity: 1;
  visibility: visible;
}

/* Swipe Gestures Support */
.swipeable {
  touch-action: pan-y;
  user-select: none;
}

/* Loading States */
.loading {
  position: relative;
  pointer-events: none;
  opacity: 0.7;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--color-neutral-300);
  border-top-color: var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Accessibility Enhancements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus Indicators */
*:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

button:focus, .btn:focus, input:focus, textarea:focus, select:focus, a:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --color-neutral-100: #ffffff;
    --color-neutral-800: #000000;
    --color-primary: #0000ff;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
`;

/**
 * Responsive utility classes
 */
const RESPONSIVE_UTILITIES = `
/* ===== RESPONSIVE UTILITY CLASSES ===== */

/* Display Utilities */
.hidden { display: none !important; }
.block { display: block !important; }
.inline { display: inline !important; }
.inline-block { display: inline-block !important; }
.flex { display: flex !important; }
.inline-flex { display: inline-flex !important; }
.grid { display: grid !important; }

/* Responsive Display */
@media (max-width: 639px) {
  .sm\\:hidden { display: none !important; }
  .sm\\:block { display: block !important; }
  .sm\\:flex { display: flex !important; }
}

@media (min-width: 640px) {
  .sm\\:hidden { display: none !important; }
  .sm\\:block { display: block !important; }
  .sm\\:flex { display: flex !important; }
}

@media (min-width: 768px) {
  .md\\:hidden { display: none !important; }
  .md\\:block { display: block !important; }
  .md\\:flex { display: flex !important; }
}

@media (min-width: 1024px) {
  .lg\\:hidden { display: none !important; }
  .lg\\:block { display: block !important; }
  .lg\\:flex { display: flex !important; }
}

/* Text Alignment */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

@media (min-width: 640px) {
  .sm\\:text-left { text-align: left; }
  .sm\\:text-center { text-align: center; }
  .sm\\:text-right { text-align: right; }
}

@media (min-width: 768px) {
  .md\\:text-left { text-align: left; }
  .md\\:text-center { text-align: center; }
  .md\\:text-right { text-align: right; }
}

/* Spacing Utilities */
.p-0 { padding: 0; }
.p-1 { padding: var(--space-xs); }
.p-2 { padding: var(--space-sm); }
.p-3 { padding: var(--space-md); }
.p-4 { padding: var(--space-lg); }

.m-0 { margin: 0; }
.m-1 { margin: var(--space-xs); }
.m-2 { margin: var(--space-sm); }
.m-3 { margin: var(--space-md); }
.m-4 { margin: var(--space-lg); }

/* Width Utilities */
.w-full { width: 100%; }
.w-auto { width: auto; }
.w-fit { width: fit-content; }

/* Height Utilities */
.h-full { height: 100%; }
.h-auto { height: auto; }
.h-screen { height: 100vh; }
.h-screen-safe { height: 100dvh; }
`;

/**
 * Complete responsive framework combining all components
 */
const COMPLETE_RESPONSIVE_FRAMEWORK =
  RESPONSIVE_CSS_FRAMEWORK +
  TOUCH_FRIENDLY_COMPONENTS +
  MOBILE_ENHANCEMENTS +
  RESPONSIVE_UTILITIES;

/**
 * Enhanced viewport meta tag for optimal mobile experience
 */
const ENHANCED_VIEWPORT_META = `<meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover, user-scalable=no">`;

/**
 * Performance and accessibility meta tags
 */
const PERFORMANCE_META_TAGS = `
<meta name="theme-color" content="#3b82f6">
<meta name="color-scheme" content="light">
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link rel="dns-prefetch" href="https://cdn.tailwindcss.com">
`;

module.exports = {
  RESPONSIVE_CSS_FRAMEWORK,
  TOUCH_FRIENDLY_COMPONENTS,
  MOBILE_ENHANCEMENTS,
  RESPONSIVE_UTILITIES,
  COMPLETE_RESPONSIVE_FRAMEWORK,
  ENHANCED_VIEWPORT_META,
  PERFORMANCE_META_TAGS
};
