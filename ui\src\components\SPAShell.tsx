/**
 * SPA Shell Component
 * Main container for the modular SPA functionality
 */

import React, { useEffect, useRef, useState } from 'react';
import { Router } from '../modules/spa/core/Router';
import { PatchManager } from '../modules/spa/core/PatchManager';
import { ComponentRegistry } from '../modules/spa/registries/ComponentRegistry';
import { ActionRegistry } from '../modules/spa/registries/ActionRegistry';
import SeamlessProgressiveRenderer from './ProgressiveRenderer';

// --- Helper functions ---
function setupEventListeners(container: HTMLElement, spaCore: any, setHighlightedEl: (el: HTMLElement | null) => void): void {
  console.log('🎯 setupEventListeners called on container:', container);
  console.log('🎯 spaCore available:', !!spaCore);
  console.log('🎯 actionRegistry available:', !!spaCore?.actionRegistry);

  // Navigation click delegation (disabled in edit mode)
  container.addEventListener('click', (e) => {
    if (spaCore.isEditMode) return;
    const target = e.target as HTMLElement;
    const navElement = target.closest('[data-nav]');
    if (navElement) {
      e.preventDefault();
      const viewName = navElement.getAttribute('data-nav');
      if (viewName && spaCore.router.hasView(viewName)) {
        spaCore.router.navigateToView(viewName);
      } else if (viewName) {
        console.warn(`⚠️ Navigation attempted to non-existent view: ${viewName}`);
      }
    }
  });

  // Action click delegation (disabled in edit mode)
  container.addEventListener('click', (e) => {
    console.log('🎯 Click event detected on container');

    if (spaCore.isEditMode) {
      console.log('🔒 Edit mode active, ignoring click');
      return;
    }

    const target = e.target as HTMLElement;
    console.log('🎯 Click target:', target);
    console.log('🎯 Target tagName:', target.tagName);
    console.log('🎯 Target attributes:', Array.from(target.attributes).map(attr => `${attr.name}="${attr.value}"`));
    console.log('🎯 Target outerHTML:', target.outerHTML);

    const actionElement = target.closest('[data-action]');
    console.log('🎯 Action element found:', actionElement);

    if (actionElement) {
      console.log('🎯 Action element found:', actionElement);
      e.preventDefault();

      const action = actionElement.getAttribute('data-action');
      const actionTarget = actionElement.getAttribute('data-target');
      const params = extractDataParams(actionElement as HTMLElement);

      console.log('🎯 Executing action:', { action, actionTarget, params });

      if (action && spaCore.actionRegistry) {
        spaCore.actionRegistry.execute(action, actionTarget, params, actionElement);
        console.log('✅ Action executed successfully');
      } else {
        console.error('❌ Action or actionRegistry not available:', { action, hasRegistry: !!spaCore.actionRegistry });
      }
    } else {
      console.log('🎯 No action element found for click');
    }
  });

  // Edit mode toggle
  const editModeBtn = container.querySelector('#editModeBtn');
  if (editModeBtn) {
    editModeBtn.addEventListener('click', () => {
      toggleEditMode(spaCore);
    });
  }

  // Diff modal controls
  const closeDiffModal = container.querySelector('#closeDiffModal');
  const applyDiff = container.querySelector('#applyDiff');
  const rejectDiff = container.querySelector('#rejectDiff');

  if (closeDiffModal) {
    closeDiffModal.addEventListener('click', () => {
      spaCore.patchManager.hideDiffModal();
    });
  }

  if (applyDiff) {
    applyDiff.addEventListener('click', () => {
      spaCore.patchManager.applyPendingDiff();
    });
  }

  if (rejectDiff) {
    rejectDiff.addEventListener('click', () => {
      spaCore.patchManager.rejectPendingDiff();
    });
  }

  // Router callbacks for component reinitialization
  spaCore.router.onAfterNavigate((viewName: string) => {
    const viewContainer = container.querySelector('#viewContainer');
    if (viewContainer) {
      spaCore.componentRegistry.reinitialize(viewContainer as HTMLElement);

      // Initialize charts after navigation
      setTimeout(() => {
        if ((window as any).initializeCharts) {
          (window as any).initializeCharts();
        }
      }, 50);
    }
  });
}
function extractDataParams(element: HTMLElement): any {
  const params: any = {};
  Array.from(element.attributes).forEach(attr => {
    if (attr.name.startsWith('data-') &&
        !['data-action', 'data-target', 'data-nav', 'data-view', 'data-component'].includes(attr.name)) {
      const key = attr.name.replace('data-', '').replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
      try {
        params[key] = JSON.parse(attr.value);
      } catch {
        params[key] = attr.value;
      }
    }
  });
  return params;
}
function toggleEditMode(spaCore: any): void {
  spaCore.isEditMode = !spaCore.isEditMode;
  const btn = document.getElementById('editModeBtn');
  if (btn) {
    if (spaCore.isEditMode) {
      btn.textContent = 'Exit Edit';
      btn.classList.add('bg-red-600', 'hover:bg-red-700');
      btn.classList.remove('bg-blue-600', 'hover:bg-blue-700');
      enableEditMode(spaCore);
    } else {
      btn.textContent = 'Edit Mode';
      btn.classList.remove('bg-red-600', 'hover:bg-red-700');
      btn.classList.add('bg-blue-600', 'hover:bg-blue-700');
      disableEditMode();
    }
  }
  console.log(`${spaCore.isEditMode ? '✏️ Enabled' : '🔒 Disabled'} edit mode`);
}
function enableEditMode(spaCore: any): void {
  document.body.classList.add('edit-mode');
}
function disableEditMode(): void {
  document.body.classList.remove('edit-mode');
}

// --- END helpers ---

interface SPAShellProps {
  className?: string;
  enableEditMode?: boolean;
  dashboardHtml?: string;
  streamingContent?: string; // ReadyAI-style streaming content
  isGenerating?: boolean; // Whether content is being generated
  onElementClick?: (element: any) => void; // Connect to main editor's element handling
  viewMode?: 'preview' | 'code';
  onViewModeChange?: (mode: 'preview' | 'code') => void;
  useSPAMode?: boolean; // Control SPA functionality
}

const SPAShellComponent = ({
  className = '',
  enableEditMode = false,
  dashboardHtml,
  streamingContent,
  isGenerating = false,
  onElementClick,
  viewMode = 'preview',
  onViewModeChange,
  useSPAMode = true
}: SPAShellProps, ref: React.Ref<HTMLDivElement>) => {
  const containerRef = useRef<HTMLDivElement>(null);

  // Helper function to get the current container element
  const getCurrentContainer = (): HTMLDivElement | null => {
    if (ref && typeof ref === 'object' && 'current' in ref) {
      return ref.current;
    }
    return containerRef.current;
  };
  const spaCore = useRef<any>(null);
  const [highlightedEl, setHighlightedEl] = useState<HTMLElement | null>(null);

  // Inject GenericSPA script for prototype functionality
  const injectGenericSPAScript = () => {
    console.log('🚀 Injecting GenericSPA script...');

    const script = document.createElement('script');
    script.setAttribute('data-spa-core', 'true');
    script.textContent = `
// GenericSPA - Auto-injected by SPAShell
class GenericSPA {
  constructor() { this.init(); }
  init() {
    document.addEventListener('click', (e) => {
      const actionEl = e.target.closest('[data-action]');
      if (actionEl) { e.preventDefault(); this.executeAction(actionEl); }
      const navEl = e.target.closest('[data-nav]');
      if (navEl) { e.preventDefault(); this.navigateToView(navEl.getAttribute('data-nav')); }
    });
    document.addEventListener('keydown', (e) => { if (e.key === 'Escape') this.closeAllModals(); });
    this.initializeAllCharts();
  }
  executeAction(el) {
    const action = el.getAttribute('data-action');
    switch (action) {
      case 'openModal': this.openModal(el.getAttribute('data-target')); break;
      case 'closeModal': this.closeAllModals(); break;
      case 'initChart': this.initChart(el); break;
      default: console.log('Action:', action);
    }
  }
  navigateToView(viewName) {
    document.querySelectorAll('[data-view]').forEach(v => v.classList.add('hidden'));
    const target = document.querySelector(\`[data-view="\${viewName}"]\`);
    if (target) { target.classList.remove('hidden'); this.updateNavStyling(viewName); setTimeout(() => { this.initializeViewCharts(viewName); window.dispatchEvent(new Event('resize')); }, 200); }
  }
  updateNavStyling(activeViewName) {
    document.querySelectorAll('[data-nav]').forEach(btn => { btn.classList.remove('border-blue-500', 'text-blue-600'); btn.classList.add('border-transparent', 'text-gray-500'); });
    const activeBtn = document.querySelector(\`[data-nav="\${activeViewName}"]\`);
    if (activeBtn) { activeBtn.classList.remove('border-transparent', 'text-gray-500'); activeBtn.classList.add('border-blue-500', 'text-blue-600'); }
  }
  openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) { modal.classList.remove('hidden'); modal.style.display = 'flex'; document.body.style.overflow = 'hidden'; }
  }
  closeAllModals() {
    document.querySelectorAll('[id$="modal"], [id$="Modal"]').forEach(modal => { modal.classList.add('hidden'); modal.style.display = 'none'; });
    document.body.style.overflow = 'auto';
  }
  // Universal chart configuration generator with robust error handling
  createChartOption(type, data, labels) {
    // Validate inputs
    if (!data || !labels || !Array.isArray(data) || !Array.isArray(labels)) {
      console.warn('Invalid chart data or labels, using fallback');
      return this.createFallbackChart();
    }

    const chartTypeMap = {
      // Basic charts
      'line': { type: 'line', needsAxis: true },
      'bar': { type: 'bar', needsAxis: true },
      'horizontalBar': { type: 'bar', needsAxis: true, horizontal: true },
      'area': { type: 'line', needsAxis: true, areaStyle: {} },
      'scatter': { type: 'scatter', needsAxis: true },

      // Circular charts
      'pie': { type: 'pie', needsAxis: false },
      'doughnut': { type: 'pie', needsAxis: false, radius: ['40%', '70%'] },
      'ring': { type: 'pie', needsAxis: false, radius: ['30%', '60%'] },

      // Specialized charts
      'radar': { type: 'radar', needsAxis: false, special: 'radar' },
      'polarArea': { type: 'pie', needsAxis: false, roseType: 'area' },
      'funnel': { type: 'funnel', needsAxis: false },
      'gauge': { type: 'gauge', needsAxis: false, special: 'gauge' },
      'sankey': { type: 'sankey', needsAxis: false, special: 'unsupported' },
      'treemap': { type: 'treemap', needsAxis: false, special: 'unsupported' },
      'sunburst': { type: 'sunburst', needsAxis: false, special: 'unsupported' },
      'graph': { type: 'graph', needsAxis: false, special: 'unsupported' },
      'heatmap': { type: 'heatmap', needsAxis: true, special: 'heatmap' },
      'candlestick': { type: 'candlestick', needsAxis: true, special: 'unsupported' },
      'boxplot': { type: 'boxplot', needsAxis: true, special: 'unsupported' }
    };

    const config = chartTypeMap[type] || chartTypeMap['bar'];

    // Handle unsupported chart types
    if (config.special === 'unsupported') {
      console.warn(\`Chart type "\${type}" not fully supported, falling back to bar chart\`);
      return this.createFallbackChart(data, labels);
    }

    let option = {
      tooltip: { trigger: config.needsAxis ? 'axis' : 'item' },
      legend: { show: true }
    };

    try {
      if (config.special === 'radar') {
        const maxValue = Math.max(...data.filter(v => typeof v === 'number')) || 100;
        option.radar = { indicator: labels.map(label => ({ name: label, max: maxValue * 1.2 })) };
        option.series = [{ type: 'radar', data: [{ value: data, name: 'Data' }] }];
      } else if (config.special === 'gauge') {
        // Gauge charts need single value
        const value = Array.isArray(data) ? data[0] : data;
        option.series = [{ type: 'gauge', data: [{ value: value, name: labels[0] || 'Value' }] }];
        delete option.legend;
      } else if (config.special === 'heatmap') {
        // Convert simple data to heatmap format - fallback to bar chart for now
        console.warn('Heatmap requires 2D coordinate data, falling back to bar chart');
        return this.createFallbackChart(data, labels);
      } else if (!config.needsAxis) {
        // Pie, doughnut, funnel, etc.
        const seriesConfig = {
          type: config.type,
          data: labels.map((label, i) => ({
            name: String(label),
            value: typeof data[i] === 'number' ? data[i] : 0
          }))
        };
        if (config.radius) seriesConfig.radius = config.radius;
        if (config.roseType) seriesConfig.roseType = config.roseType;
        option.series = [seriesConfig];
      } else {
        // Line, bar, scatter, area, etc.
        if (config.horizontal) {
          option.xAxis = { type: 'value' };
          option.yAxis = { type: 'category', data: labels.map(String) };
        } else {
          option.xAxis = { type: 'category', data: labels.map(String) };
          option.yAxis = { type: 'value' };
        }
        const seriesConfig = {
          type: config.type,
          data: data.filter(v => typeof v === 'number'),
          itemStyle: { color: '#3b82f6' }
        };
        if (config.areaStyle) seriesConfig.areaStyle = config.areaStyle;
        if (type === 'line' || type === 'area') seriesConfig.smooth = true;
        option.series = [seriesConfig];
      }
    } catch (error) {
      console.error('Error creating chart option:', error);
      return this.createFallbackChart(data, labels);
    }

    return option;
  }

  // Fallback chart for when things go wrong
  createFallbackChart(data = [1, 2, 3], labels = ['A', 'B', 'C']) {
    return {
      tooltip: { trigger: 'axis' },
      xAxis: { type: 'category', data: labels.map(String) },
      yAxis: { type: 'value' },
      series: [{
        type: 'bar',
        data: data.filter(v => typeof v === 'number' && !isNaN(v)),
        itemStyle: { color: '#3b82f6' }
      }]
    };
  }

  initChart(el) {
    const chartId = el.getAttribute('data-chart-id');
    const chartType = el.getAttribute('data-chart-type');
    const chartData = el.getAttribute('data-chart-data');
    const chartLabels = el.getAttribute('data-chart-labels');

    if (!window.echarts) {
      console.warn('ECharts not loaded yet');
      return;
    }

    if (!chartData || !chartLabels || !chartId) {
      console.warn('Missing chart attributes:', { chartId, chartType, hasData: !!chartData, hasLabels: !!chartLabels });
      return;
    }

    try {
      // Robust JSON parsing with fallbacks
      let data, labels;

      try {
        data = JSON.parse(chartData);
      } catch (e) {
        console.warn('Invalid chart data JSON, using fallback:', chartData);
        data = [1, 2, 3]; // Fallback data
      }

      try {
        labels = JSON.parse(chartLabels);
      } catch (e) {
        console.warn('Invalid chart labels JSON, using fallback:', chartLabels);
        labels = ['A', 'B', 'C']; // Fallback labels
      }

      const container = document.getElementById(chartId);
      if (!container) {
        console.warn('Chart container not found:', chartId);
        return;
      }

      // Handle hidden containers (deferred initialization)
      if (container.offsetWidth === 0) {
        console.log('📊 Deferring chart initialization (hidden container):', chartId);
        container.setAttribute('data-chart-config', JSON.stringify({chartType, data, labels}));
        return;
      }

      // Initialize chart
      const chart = echarts.init(container);
      const option = this.createChartOption(chartType, data, labels);
      chart.setOption(option);

      // Handle window resize
      const resizeHandler = () => chart.resize();
      window.addEventListener('resize', resizeHandler);

      console.log('✅ Chart initialized successfully:', chartId);

    } catch (error) {
      console.error('❌ Chart initialization failed:', chartId, error);
    }
  }
  initializeAllCharts() { document.querySelectorAll('[data-action="initChart"]').forEach(el => this.initChart(el)); }
  initializeViewCharts(viewName) {
    const view = document.querySelector(\`[data-view="\${viewName}"]\`);
    if (view) {
      console.log('🎯 Initializing charts for view:', viewName);

      // Force re-initialization of all charts in this view
      view.querySelectorAll('[data-action="initChart"]').forEach(el => {
        console.log('🔄 Re-initializing chart:', el.getAttribute('data-chart-id'));
        this.initChart(el);
      });

      // Handle deferred charts
      view.querySelectorAll('[data-chart-config]').forEach(container => {
        try {
          const config = JSON.parse(container.getAttribute('data-chart-config'));
          console.log('📊 Processing deferred chart:', container.id, 'width:', container.offsetWidth);
          if (window.echarts && container.offsetWidth > 0) {
            const chart = echarts.init(container);
            const option = this.createChartOption(config.chartType, config.data, config.labels);
            chart.setOption(option); container.removeAttribute('data-chart-config');
            console.log('✅ Deferred chart initialized:', container.id);
          } else {
            console.log('⏳ Chart still not ready:', container.id, 'width:', container.offsetWidth);
          }
        } catch (error) { console.error('Deferred chart error:', error); }
      });
    }
  }
}
if (!window.genericSPA) { window.genericSPA = new GenericSPA(); console.log('✅ GenericSPA auto-injected and initialized'); }
`;

    document.head.appendChild(script);
    console.log('✅ GenericSPA script injected successfully');
  };
  const [librariesLoaded, setLibrariesLoaded] = useState(false);


  // Debug: Log the dashboardHtml content, streaming, and SPA mode
  useEffect(() => {
    console.log('🔍 SPAShell state:', {
      useSPAMode,
      hasContent: !!dashboardHtml,
      hasStreamingContent: !!streamingContent,
      isGenerating,
      length: dashboardHtml?.length || 0,
      streamingLength: streamingContent?.length || 0,
      preview: dashboardHtml?.substring(0, 100) || 'No content',
      viewMode,
      isEmptyString: dashboardHtml === '',
      isOnlyWhitespace: dashboardHtml?.trim() === ''
    });
  }, [dashboardHtml, streamingContent, isGenerating, viewMode, useSPAMode]);

  // Inject GenericSPA script after HTML content is rendered
  useEffect(() => {
    if (dashboardHtml?.trim() && useSPAMode) {
      // Wait for DOM to be updated, then inject script
      const timer = setTimeout(() => {
        if (!(window as any).genericSPA && !document.querySelector('script[data-spa-core]')) {
          console.log('🚀 Injecting GenericSPA script after HTML render...');
          injectGenericSPAScript();
        }
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [dashboardHtml, useSPAMode]);

  // Load external libraries and CSS
  useEffect(() => {
    const loadLibraries = async () => {
      console.log('🔧 Loading common libraries and CSS...');

      // 1. Load Tailwind CSS if not already loaded
      if (!document.querySelector('link[href*="tailwindcss"]')) {
        const tailwindLink = document.createElement('link');
        tailwindLink.rel = 'stylesheet';
        tailwindLink.href = 'https://cdn.tailwindcss.com';
        document.head.appendChild(tailwindLink);
        console.log('🎨 Tailwind CSS loaded');
      }

      // 2. Load ECharts if not already loaded
      if (!(window as any).echarts) {
        const echartsScript = document.createElement('script');
        echartsScript.src = 'https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js';
        echartsScript.async = true;
        echartsScript.onload = () => {
          console.log('📊 ECharts library loaded');
          initializeCharts();
        };
        echartsScript.onerror = () => {
          console.error('❌ Failed to load ECharts library');
          // Continue without charts
        };
        document.head.appendChild(echartsScript);
      } else {
        initializeCharts();
      }

      // 3. Load Font Awesome for icons (optional)
      if (!document.querySelector('link[href*="font-awesome"]')) {
        const fontAwesome = document.createElement('link');
        fontAwesome.rel = 'stylesheet';
        fontAwesome.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css';
        document.head.appendChild(fontAwesome);
        console.log('🎯 Font Awesome loaded');
      }

      // 4. Inject GenericSPA script if not already present
      if (!(window as any).genericSPA && !document.querySelector('script[data-spa-core]')) {
        injectGenericSPAScript();
      }

      // 4. Add custom CSS for better styling
      if (!document.querySelector('#spa-shell-styles')) {
        const customStyles = document.createElement('style');
        customStyles.id = 'spa-shell-styles';
        customStyles.textContent = `
          /* SPA Shell Custom Styles */
          .spa-shell {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          }

          /* Force navigation container styling */
          .spa-shell #llmNavigation nav {
            background-color: white !important;
            color: inherit !important;
            border-bottom: 1px solid #e5e7eb !important;
          }

          /* Enhanced navigation styling */
          .spa-shell nav button[data-nav] {
            position: relative;
            transition: all 0.2s ease;
          }

          .spa-shell nav button[data-nav]:hover {
            background-color: rgba(59, 130, 246, 0.05);
            border-bottom-color: #3b82f6;
          }

          .spa-shell nav button[data-nav].active {
            color: #3b82f6;
            border-bottom-color: #3b82f6;
            background-color: rgba(59, 130, 246, 0.05);
          }

          /* Smooth transitions for all interactive elements */
          .spa-shell button, .spa-shell a, .spa-shell [data-action] {
            transition: all 0.2s ease;
          }

          /* Modal backdrop blur effect */
          .spa-shell .modal-backdrop {
            backdrop-filter: blur(4px);
          }

          /* Chart container styling */
          .spa-shell [id*="Chart"] {
            border-radius: 8px;
            overflow: hidden;
          }

          /* Loading animations */
          @keyframes spa-pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
          }

          .spa-loading {
            animation: spa-pulse 2s infinite;
          }          /* Navigation responsive design */
          @media (max-width: 640px) {
            .spa-shell nav .flex.space-x-2 {
              flex-wrap: wrap;
              gap: 0.25rem;
            }

            .spa-shell nav button[data-nav] {
              padding: 0.5rem 0.75rem;
              font-size: 0.75rem;
            }
          }

          /* Code container styling */
          .spa-shell pre {
            font-size: 0.875rem;
            line-height: 1.5;
            color: #1e293b;
            background-color: #f8fafc;
            transition: all 0.2s ease;
          }

          .spa-shell pre:hover {
            box-shadow: inset 0 0 0 1px rgba(99, 102, 241, 0.1);
          }

          .spa-shell pre code {
            font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
          }
        `;
        document.head.appendChild(customStyles);
        console.log('🎨 Custom SPA styles loaded');
      }

      console.log('✅ All libraries and CSS loaded successfully');
      setLibrariesLoaded(true);
    };

    loadLibraries();
  }, []);

  // Force apply navigation styling to existing content
  const applyNavigationStyling = () => {
    console.log('🎨 Applying navigation styling to existing content...');

    // Find all navigation buttons
    const navButtons = document.querySelectorAll('[data-nav]');
    console.log(`🔍 Found ${navButtons.length} navigation buttons`);

    navButtons.forEach(button => {
      // Remove old classes and add new ones
      button.classList.remove('py-2', 'px-1');
      button.classList.add(
        'border-transparent', 'text-gray-500', 'hover:text-gray-700',
        'hover:border-gray-300', 'whitespace-nowrap', 'py-2', 'px-4',
        'mx-1', 'border-b-2', 'font-medium', 'text-sm',
        'transition-colors', 'duration-200', 'rounded-t-lg', 'hover:bg-gray-50'
      );
      console.log('✅ Applied styling to button:', button.textContent);
    });

    // Apply styling to navigation container
    const nav = document.querySelector('#llmNavigation nav') as HTMLElement;
    if (nav) {
      // Remove any dark/black background classes
      nav.classList.remove('bg-black', 'bg-gray-900', 'bg-gray-800', 'bg-blue-900', 'bg-slate-900');
      // Force white background and proper styling
      nav.classList.add('bg-white', 'shadow-sm', 'border-b', 'border-gray-200');
      nav.style.backgroundColor = 'white';
      nav.style.color = 'inherit';
      console.log('✅ Applied styling to navigation container');
    }
  };

  // DISABLED: Chart placeholder conversion (was causing page replacement issues)
  const convertPlaceholdersToCharts = () => {
    console.log('⚠️ Chart placeholder conversion disabled - LLM should generate proper chart elements');
    // This function was replacing entire page content
    // Charts should come from LLM with proper data-action="initChart" attributes
  };



  // Initialize charts function with error handling
  const initializeCharts = () => {
    if (!(window as any).echarts) {
      console.warn('⚠️ ECharts not loaded yet, skipping chart initialization');
      return;
    }

    const chartElements = document.querySelectorAll('[data-action="initChart"]');
    console.log(`📊 Found ${chartElements.length} charts to initialize`);

    chartElements.forEach(element => {
      try {
        const chartId = element.getAttribute('data-chart-id');
        const chartType = element.getAttribute('data-chart-type') || 'line';
        const chartData = JSON.parse(element.getAttribute('data-chart-data') || '[]');
        const chartLabels = JSON.parse(element.getAttribute('data-chart-labels') || '[]');

        if (chartId) {
          const chartContainer = document.getElementById(chartId);
          if (chartContainer) {
            const chart = (window as any).echarts.init(chartContainer);
            const option = {
              title: { text: element.getAttribute('data-chart-title') || 'Chart' },
              tooltip: { trigger: 'axis' },
              xAxis: { type: 'category', data: chartLabels },
              yAxis: { type: 'value' },
              series: [{
                type: chartType,
                data: chartData,
                smooth: chartType === 'line',
                itemStyle: { color: '#3b82f6' }
              }]
            };
            chart.setOption(option);

            // Make chart responsive
            window.addEventListener('resize', () => chart.resize());
            console.log(`📊 Chart initialized: ${chartId}`);
          } else {
            console.warn(`⚠️ Chart container not found: ${chartId}`);
          }
        }
      } catch (error) {
        console.error('❌ Error initializing chart:', error);
      }
    });
  };

  useEffect(() => {
    const container = getCurrentContainer();
    if (!container) return;

    // Only initialize SPA Core System when SPA mode is enabled
    if (!useSPAMode) {
      console.log('📄 SPA mode disabled - showing simple preview');
      return;
    }

    console.log('🌐 SPA mode enabled - initializing full SPA functionality');

    // Initialize SPA Core System
    const componentRegistry = new ComponentRegistry();
    const actionRegistry = new ActionRegistry();
    const router = new Router();
    const patchManager = new PatchManager();

    // Store in ref for cleanup
    spaCore.current = {
      componentRegistry,
      actionRegistry,
      router,
      patchManager,
      isEditMode: enableEditMode
    };

    // Make globally available for compatibility
    (window as any).spaCore = spaCore.current;

    // Expose test methods for debugging
    (window as any).testPatchManager = () => {
      spaCore.current.patchManager.testPatchApplication();
    };

    // Expose simulation method for testing with exact API data
    (window as any).simulateApiResponse = () => {
      spaCore.current.patchManager.simulateApiResponse();
    };

    // Expose navigation styling function for manual triggering
    (window as any).applyNavigationStyling = applyNavigationStyling;

    // Expose chart initialization function for manual triggering
    (window as any).initializeCharts = initializeCharts;

    // Expose chart placeholder conversion function
    (window as any).convertPlaceholdersToCharts = convertPlaceholdersToCharts;

    // Expose chart debugging function
    (window as any).debugCharts = () => {
      console.log('🔍 Chart Debug Info:');
      console.log('ECharts loaded:', !!(window as any).echarts);
      console.log('Chart elements found:', document.querySelectorAll('[data-action="initChart"]').length);
      console.log('Chart containers found:', document.querySelectorAll('[id*="Chart"]').length);

      // List all chart-related elements
      const chartElements = document.querySelectorAll('[data-action="initChart"]');
      chartElements.forEach((el, index) => {
        console.log(`Chart ${index + 1}:`, {
          chartId: el.getAttribute('data-chart-id'),
          chartType: el.getAttribute('data-chart-type'),
          hasContainer: !!document.getElementById(el.getAttribute('data-chart-id') || '')
        });
      });
    };



    // Expose modal debugging function
    (window as any).debugModals = () => {
      console.log('🎭 Modal Debug Info:');
      const modalTriggers = document.querySelectorAll('[data-action="openModal"]');
      console.log(`Found ${modalTriggers.length} modal trigger buttons:`);

      modalTriggers.forEach((trigger, index) => {
        const targetId = trigger.getAttribute('data-target');
        const modal = targetId ? document.getElementById(targetId) : null;
        console.log(`Modal Trigger ${index + 1}:`, {
          text: trigger.textContent?.trim(),
          targetId,
          hasModal: !!modal,
          trigger,
          modal
        });
      });

      const allModals = document.querySelectorAll('[id$="Modal"], .modal, [role="dialog"]');
      console.log(`Found ${allModals.length} modal elements in DOM`);
    };



    // Initialize default views with SPA mode awareness
    initializeDefaultViews(router, dashboardHtml, useSPAMode);

    // Force apply navigation styling and initialize functionality
    setTimeout(() => {
      applyNavigationStyling();
      initializeCharts(); // Initialize any existing chart elements
      
      // Auto-trigger renderChart actions for charts in the DOM
      const chartElements = document.querySelectorAll('[data-action="renderChart"]');
      chartElements.forEach(el => {
        if (spaCore.current?.actionRegistry) {
          spaCore.current.actionRegistry.execute('renderChart', null, {}, el as HTMLElement);
        }
      });
      
      console.log(`🚀 Auto-initialized ${chartElements.length} charts`);
    }, 100);

    // Setup event listeners
    const containerElement = getCurrentContainer();
    if (containerElement) {
      setupEventListeners(containerElement, spaCore.current, setHighlightedEl);
    }

    // Set up automatic navigation activation when content is ready
    if (router.hasViews()) {
      // Use a longer delay to ensure content is fully rendered
      setTimeout(() => {
        const container = getCurrentContainer();
        if (container) {
          // Find the first visible section or default to dashboard
          const visibleSection = container.querySelector('[data-view]:not(.hidden)');
          const firstSection = container.querySelector('[data-view]');
          const defaultViewName = visibleSection?.getAttribute('data-view') || 
                                 firstSection?.getAttribute('data-view') || 
                                 'dashboard';
          
          if (defaultViewName && router.hasView(defaultViewName)) {
            console.log(`🎯 Auto-activating default view: ${defaultViewName}`);
            router.navigateToView(defaultViewName);
          }
        }
      }, 300);
    } else {
      console.log('📄 No views available - waiting for LLM content');
    }

    console.log('🚀 SPA Shell initialized');

    // Cleanup
    return () => {
      if (useSPAMode) {
        componentRegistry.clear();
        actionRegistry.clear();
        delete (window as any).spaCore;
        console.log('🧹 SPA Shell cleaned up');
      }
    };
  }, [enableEditMode, dashboardHtml, useSPAMode]);







  // Sync edit mode state when prop changes
  useEffect(() => {
    if (spaCore.current) {
      spaCore.current.isEditMode = enableEditMode;
      console.log(`🔄 SPAShell edit mode synced: ${enableEditMode ? 'enabled' : 'disabled'}`);

      // Update button appearance to match state
      const btn = document.getElementById('editModeBtn');
      if (btn) {
        if (enableEditMode) {
          btn.textContent = 'Exit Edit';
          btn.classList.add('bg-red-600', 'hover:bg-red-700');
          btn.classList.remove('bg-blue-600', 'hover:bg-blue-700');
          enableEditMode && document.body.classList.add('edit-mode');
        } else {
          btn.textContent = 'Edit Mode';
          btn.classList.remove('bg-red-600', 'hover:bg-red-700');
          btn.classList.add('bg-blue-600', 'hover:bg-blue-700');
          document.body.classList.remove('edit-mode');
        }
      }
    }
  }, [enableEditMode]);

  // Highlight selected element for editing
  useEffect(() => {
    document.querySelectorAll('[data-spa-edit-highlight]').forEach(el => {
      el.removeAttribute('data-spa-edit-highlight');
      (el as HTMLElement).style.outline = '';
      (el as HTMLElement).style.outlineOffset = '';
      (el as HTMLElement).style.zIndex = '';
      (el as HTMLElement).style.position = '';
    });

    if (!highlightedEl) return;
    highlightedEl.setAttribute('data-spa-edit-highlight', 'true');
    highlightedEl.style.outline = '3px solid #2563eb !important';
    highlightedEl.style.outlineOffset = '2px';
    highlightedEl.style.zIndex = '9999';
    highlightedEl.style.position = 'relative';

    return () => {
      highlightedEl.removeAttribute('data-spa-edit-highlight');
      highlightedEl.style.outline = '';
      highlightedEl.style.outlineOffset = '';
      highlightedEl.style.zIndex = '';
      highlightedEl.style.position = '';
    };
  }, [highlightedEl]);

  // --- GLOBAL EDIT HANDLER ---
  function globalEditHandler(e: MouseEvent) {
    const spaCore = (window as any).spaCore;
    const target = e.target as HTMLElement;

    console.log('🔥 globalEditHandler called:', {
      spaCore: !!spaCore,
      isEditMode: spaCore?.isEditMode,
      enableEditModeProp: enableEditMode,
      target: target?.tagName,
      hasDataAction: !!target?.closest('[data-action]'),
      hasDataNav: !!target?.closest('[data-nav]')
    });

    // Allow data-action and data-nav clicks to proceed even when edit mode is not active
    const actionElement = target.closest('[data-action]');
    const navElement = target.closest('[data-nav]');

    if (actionElement || navElement) {
      console.log('🔥 Allowing data-action/data-nav click to proceed');
      return; // Let the click proceed to normal handlers
    }

    if (!spaCore?.isEditMode) {
      console.log('🔥 Edit mode not active, ignoring click');
      return;
    }

    // Ignore clicks inside modals, popups, or UI overlays
    const isInsideModal = target.closest('.fixed.inset-0') || // Modal overlay
                         target.closest('[role="dialog"]') || // Dialog elements
                         target.closest('.modal') || // Generic modal class
                         target.closest('.z-50'); // High z-index overlays

    if (isInsideModal) {
      console.log('🔥 Click inside modal/overlay, ignoring in edit mode');
      return;
    }

    e.preventDefault();
    e.stopPropagation();
    if (typeof e.stopImmediatePropagation === 'function') e.stopImmediatePropagation();
    const editable = target.closest('[data-editable], button, section, div') as HTMLElement | null;
    if (!editable) return;

    setHighlightedEl(editable);

    let selector = '';
    if (editable.id) {
      selector = `#${editable.id}`;
    } else if (editable.getAttribute('data-component')) {
      selector = `[data-component="${editable.getAttribute('data-component')}"]`;
    } else if (editable.className) {
      selector = `${editable.tagName.toLowerCase()}.${editable.className.split(' ').join('.')}`;
    } else {
      const parent = editable.parentElement;
      if (parent) {
        const children = Array.from(parent.children);
        const idx = children.indexOf(editable) + 1;
        selector = `${editable.tagName.toLowerCase()}:nth-child(${idx})`;
      } else {
        selector = editable.tagName.toLowerCase();
      }
    }

    console.log('🔥 SPAShell edit mode click:', {
      element: editable.tagName,
      text: editable.textContent?.trim(),
      selector: selector,
      outerHTML: editable.outerHTML.slice(0, 100)
    });

    // If onElementClick callback is provided, use main editor's system
    if (onElementClick) {
      console.log('🔥 Using main editor element handling system');

      // Prepare element data in the format expected by main editor
      const elementData = {
        tagName: editable.tagName.toLowerCase(),
        textContent: editable.textContent?.trim() || '',
        className: editable.className || '',
        id: editable.id || '',
        outerHTML: editable.outerHTML,
        isInteractive: true, // Mark as interactive to trigger implementation modal
        implementationType: 'edit',
        implementationReason: `Edit ${editable.tagName.toLowerCase()}: "${editable.textContent?.trim() || 'element'}"`,
        selector: selector
      };

      // Call the main editor's element click handler
      onElementClick(elementData);
      return;
    }

    // Fallback to original PatchManager system if no callback provided
    console.log('🔥 Using fallback PatchManager system');
    const fragmentHtml = editable.outerHTML;
    const section = editable.closest('[data-view]');
    const viewName = section ? section.getAttribute('data-view') : undefined;
    const prompt = `Edit the selected element: ${editable.textContent?.trim() || editable.tagName}`;

    if (viewName) {
      spaCore.patchManager.applyLlmPatchToView(viewName, fragmentHtml, prompt, selector);
    }
  }

  useEffect(() => {
    document.addEventListener('click', globalEditHandler, true);
    return () => {
      document.removeEventListener('click', globalEditHandler, true);
    };
  }, [onElementClick]); // Add onElementClick to dependencies

  return (
    <div
      ref={(element) => {
        // Set internal ref
        (containerRef as any).current = element;

        // Set forwarded ref
        if (ref) {
          if (typeof ref === 'function') {
            ref(element);
          } else {
            (ref as any).current = element;
          }
        }
      }}
      className={`spa-shell ${className}`}
    >
      {/* Loading Indicator for Libraries */}
      {!librariesLoaded && (
        <div className="fixed inset-0 bg-white bg-opacity-90 flex items-center justify-center z-50">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 font-medium">Loading libraries...</p>
            <p className="text-sm text-gray-500">Tailwind CSS, ECharts, Font Awesome</p>
          </div>
        </div>
      )}      {/* Fixed Infrastructure - CSS and JS Libraries */}
      <div style={{ display: 'none' }}>
        {/* Libraries loaded via useEffect */}
        {/* Tailwind CSS: https://cdn.tailwindcss.com */}
        {/* ECharts: https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js */}
        {/* Font Awesome: https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css */}
      </div>



      {/* Seamless Progressive HTML Rendering */}
      <div id="llmNavigation" style={{ display: viewMode === 'code' ? 'none' : 'block' }}>
        {(() => {
          console.log('🔍 SPAShell rendering decision:', {
            hasStreamingContent: !!streamingContent,
            streamingContentLength: streamingContent?.length || 0,
            hasDashboardHtml: !!dashboardHtml?.trim(),
            dashboardHtmlLength: dashboardHtml?.length || 0,
            useSPAMode,
            isGenerating,
            viewMode,
            willUseProgressiveRenderer: !!(streamingContent && useSPAMode),
            willUseDirectRender: !!(dashboardHtml?.trim() && useSPAMode && !streamingContent),
            willShowEmpty: !!(useSPAMode && !streamingContent && !dashboardHtml?.trim())
          });
          return null;
        })()}
        {streamingContent && useSPAMode ? (
          /* Progressive rendering during generation */
          <SeamlessProgressiveRenderer
            htmlContent={dashboardHtml}
            streamingContent={streamingContent}
            isGenerating={isGenerating}
            onElementClick={onElementClick}
            className="h-full w-full"
          />
        ) : dashboardHtml?.trim() && useSPAMode ? (
          /* Direct rendering for existing content */
          (() => {
            console.log('🎯 SPAShell: Using DIRECT rendering for existing content (NO progressive rendering)');
            return (
              <div
                id="viewContainer"
                className="h-full w-full"
                dangerouslySetInnerHTML={{ __html: dashboardHtml }}
                onClick={onElementClick ? (e) => {
              // Handle element clicks for existing content
              const target = e.target as HTMLElement;
              if (enableEditMode && onElementClick) {
                onElementClick({
                  tagName: target.tagName.toLowerCase(),
                  textContent: target.textContent?.trim() || '',
                  className: target.className || '',
                  id: target.id || '',
                  outerHTML: target.outerHTML,
                  isInteractive: true,
                  implementationType: 'edit',
                  implementationReason: `Edit ${target.tagName.toLowerCase()}: "${target.textContent?.trim() || 'element'}"`,
                  selector: target.id ? `#${target.id}` : target.tagName.toLowerCase()
                });
              }
            } : undefined}
          />
            );
          })()
        ) : useSPAMode ? (
          /* Empty state when no content is available */
          <div className="h-full flex items-center justify-center bg-gray-50">
            <div className="text-center max-w-md mx-auto p-8">
              <div className="mb-6">
                <svg className="w-16 h-16 mx-auto text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No Content Generated</h3>
              <p className="text-gray-600 mb-6">
                Generate some content to see your dynamic navigation and application interface.
              </p>
              <div className="text-sm text-gray-500">
                <p className="mb-2"><strong>The LLM will create:</strong></p>
                <ul className="text-left space-y-1">
                  <li>• Context-aware navigation (e.g., Contacts, Opportunities)</li>
                  <li>• Business-specific sections and views</li>
                  <li>• Interactive charts and modals</li>
                  <li>• Professional UI with Tailwind CSS</li>
                </ul>
              </div>
            </div>
          </div>
        ) : null}
      </div>

      {/* Main Content */}
      <main className="flex-1">
        {(() => {
          console.log('🔍 SPAShell render - viewMode:', viewMode, 'useSPAMode:', useSPAMode, 'dashboardHtml length:', dashboardHtml?.length || 0, 'streamingContent length:', streamingContent?.length || 0, 'isGenerating:', isGenerating);
          return null;
        })()}        {/* CODE VIEW */}
        {viewMode === 'code' && (
          <div className="h-full bg-gray-50 p-4 flex items-center justify-center">
            <div className="max-w-4xl w-full bg-white rounded-lg shadow-md border border-gray-200 h-full flex flex-col">
              <div className="flex items-center justify-between p-4 border-b bg-gray-50 rounded-t-lg">
                <h3 className="text-lg font-medium text-gray-900">HTML Source Code</h3>
                <button
                  onClick={() => {
                    if (dashboardHtml) {
                      navigator.clipboard.writeText(dashboardHtml);
                      console.log('📋 HTML copied to clipboard');
                    }
                  }}
                  className="px-3 py-1.5 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 transition-colors"
                >
                  📋 Copy Code
                </button>
              </div>
              <div className="flex-1 overflow-auto">
                {dashboardHtml ? (
                  <div className="p-4">                    <div className="mb-4 text-sm text-gray-600">
                      <span className="font-medium">{dashboardHtml.length.toLocaleString()}</span> characters
                    </div>
                    <pre className="p-4 text-sm text-gray-800 font-mono leading-relaxed whitespace-pre-wrap bg-gray-50 border border-gray-200 rounded-lg overflow-auto shadow-inner">
                      <code className="selection:bg-indigo-100">{dashboardHtml}</code>
                    </pre>
                  </div>
                ) : (
                  <div className="p-8 text-center text-gray-500">
                    <div className="mb-4">
                      <svg className="w-16 h-16 mx-auto text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No HTML Content</h3>
                    <p className="text-sm text-gray-600 mb-4">
                      Generate some content first to view the HTML source code.
                    </p>
                    <button
                      onClick={() => onViewModeChange?.('preview')}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 transition-colors"
                    >
                      Switch to Preview Mode
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        
      </main>

      {/* Diff Modal */}
      <div id="diffModal" className="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
          <div className="mt-3">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Review Changes
            </h3>
            <div id="diffContent" className="mt-2 max-h-96 overflow-y-auto bg-gray-50 p-4 rounded border text-sm">
              {/* Diff content will be populated */}
            </div>
            <div className="flex justify-end space-x-3 mt-6">
              <button
                id="rejectDiff"
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
              >
                Reject
              </button>
              <button
                id="applyDiff"
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Apply Changes
              </button>
            </div>
          </div>
          <button
            id="closeDiffModal"
            className="absolute top-3 right-3 text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      {/* Loading Overlay */}
      <div id="loadingOverlay" className="hidden fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-40">
        <div className="bg-white rounded-lg p-6 shadow-xl">
          <div className="flex items-center space-x-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="text-gray-700">Processing changes...</span>
          </div>
        </div>
      </div>      {/* Highlight style */}
      <style>{`
        [data-spa-edit-highlight] {
          outline: 3px solid #2563eb !important;
          outline-offset: 2px !important;
          z-index: 9999 !important;
          position: relative !important;
          transition: outline 0.2s;
        }
        
        /* Modern scrollbar styling */
        ::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }
        
        ::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 8px;
        }
        
        ::-webkit-scrollbar-thumb {
          background: #c5c5c5;
          border-radius: 8px;
          transition: background 0.2s ease;
        }
        
        ::-webkit-scrollbar-thumb:hover {
          background: #a0a0a0;
        }
        
        /* For Firefox */
        * {
          scrollbar-width: thin;
          scrollbar-color: #c5c5c5 #f1f1f1;
        }
        
        /* Specific styling for code view pre element */
        pre::-webkit-scrollbar {
          width: 6px;
          height: 6px;
        }
        
        pre::-webkit-scrollbar-thumb {
          background: rgba(99, 102, 241, 0.4);
          border-radius: 6px;
        }
        
        pre::-webkit-scrollbar-thumb:hover {
          background: rgba(99, 102, 241, 0.6);
        }
        
        pre {
          scrollbar-width: thin;
          scrollbar-color: rgba(99, 102, 241, 0.4) #f1f1f1;
        }
      `}</style>
    </div>
  );
};

export const SPAShell = React.forwardRef<HTMLDivElement, SPAShellProps>(SPAShellComponent);

/**
 * Initialize default views - Updated to work with AnimatedHtmlRenderer and StreamingHtmlRenderer
 */
function initializeDefaultViews(router: Router, dashboardHtml?: string, useSPAMode: boolean = true): void {
  console.log('🔧 initializeDefaultViews called with useSPAMode:', useSPAMode);

  // If SPA mode is disabled, don't process views
  if (!useSPAMode) {
    console.log('📄 SPA mode disabled - skipping view initialization');
    return;
  }

  // If dashboardHtml is provided, extract and register views with the router
  if (dashboardHtml && dashboardHtml.trim()) {
    console.log('🎬 Processing HTML for router views (AnimatedHtmlRenderer/StreamingHtmlRenderer will handle rendering)');

    // Process the LLM content to extract sections for router
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = dashboardHtml;

    // Find all sections with data-view attributes for router registration
    const sections = tempDiv.querySelectorAll('[data-view]');
    console.log(`🔍 Found ${sections.length} sections with data-view attributes`);

    sections.forEach(section => {
      const viewName = section.getAttribute('data-view');
      const viewTitle = viewName ? viewName.charAt(0).toUpperCase() + viewName.slice(1) : 'View';
      if (viewName) {
        router.addView(viewName, section.innerHTML, viewTitle);
        console.log(`📍 Added view: ${viewName}`);
      }
    });

    // If no sections found, treat the entire content as the main view
    if (sections.length === 0) {
      console.log('📍 No data-view sections found, creating default dashboard view');
      router.addView('dashboard', dashboardHtml, 'Dashboard');
    }

    // Find the first visible section to set as default, or use dashboard
    const visibleSection = Array.from(sections).find(section =>
      !section.classList.contains('hidden')
    );

    const defaultViewName = visibleSection?.getAttribute('data-view') || 'dashboard';
    if (defaultViewName && router.hasView(defaultViewName)) {
      console.log(`🎯 Default view identified: ${defaultViewName} (will be activated by navigation system)`);
      // Don't navigate immediately - let the tab structure load first
      // The navigation will be handled by the first tab click or automatic activation
    }
  } else {
    // No content provided - AnimatedHtmlRenderer will show empty state
    console.log('📄 No content provided - AnimatedHtmlRenderer will handle empty state');
  }
}

export default SPAShell;
