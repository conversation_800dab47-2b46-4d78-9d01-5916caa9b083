// intentApiService.ts
// Service for intent-related API calls (LLM V3)

// API base URL configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api';

export interface Intent {
  userIntent: string;
  suggestion?: string;
  [key: string]: any;
}

export async function generateIntent({
  htmlContent,
  elementCode,
  conversationHistory = [],
}: {
  htmlContent: string;
  elementCode: string;
  conversationHistory?: any[];
}): Promise<{ success: boolean; intent?: Intent; error?: string }> {
  try {
    const response = await fetch(`${API_BASE_URL}/llm/v3/generate-intent`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
      body: JSON.stringify({
        htmlContent,
        elementCode,
        conversationHistory,
      }),
    });
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    const data = await response.json();
    return data;
  } catch (error: any) {
    return { success: false, error: error.message || 'Failed to generate intent' };
  }
}
