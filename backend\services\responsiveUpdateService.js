/**
 * Responsive Update Service
 * 
 * This service systematically updates all existing pages in the database
 * with responsive design improvements while preserving their core functionality.
 */

const { pool } = require('./promptDbService');
const prototypePageService = require('./prototypePageService');
const prototypeService = require('./prototypeService');
const { COMPLETE_RESPONSIVE_FRAMEWORK } = require('../config/responsiveFramework');

class ResponsiveUpdateService {
  
  /**
   * Update all pages for all users with responsive design improvements
   * @param {Object} options - Update options
   * @param {boolean} options.dryRun - If true, only log what would be updated without making changes
   * @param {number} options.batchSize - Number of pages to process in each batch
   * @returns {Promise<Object>} Update results
   */
  async updateAllPages(options = {}) {
    const { dryRun = false, batchSize = 50 } = options;
    
    console.log(`🚀 [ResponsiveUpdateService] Starting ${dryRun ? 'DRY RUN' : 'LIVE UPDATE'} of all pages`);
    
    const results = {
      totalPages: 0,
      updatedPages: 0,
      skippedPages: 0,
      errors: [],
      startTime: new Date(),
      endTime: null
    };
    
    try {
      // Get total count of all pages
      const countQuery = 'SELECT COUNT(*) as total FROM prototype_pages';
      const countResult = await pool.query(countQuery);
      results.totalPages = parseInt(countResult.rows[0].total);
      
      console.log(`📊 Found ${results.totalPages} total pages to process`);
      
      if (results.totalPages === 0) {
        console.log('✅ No pages found to update');
        results.endTime = new Date();
        return results;
      }
      
      // Process pages in batches
      let offset = 0;
      while (offset < results.totalPages) {
        console.log(`📦 Processing batch ${Math.floor(offset / batchSize) + 1}/${Math.ceil(results.totalPages / batchSize)}`);
        
        const batchQuery = `
          SELECT pp.*, p.title as prototype_title, p.user_id
          FROM prototype_pages pp
          JOIN prototypes p ON pp.prototype_id = p.id
          ORDER BY pp.id
          LIMIT $1 OFFSET $2
        `;
        
        const batchResult = await pool.query(batchQuery, [batchSize, offset]);
        const pages = batchResult.rows;
        
        for (const page of pages) {
          try {
            const updateResult = await this.updateSinglePage(page, dryRun);
            if (updateResult.updated) {
              results.updatedPages++;
            } else {
              results.skippedPages++;
            }
          } catch (error) {
            console.error(`❌ Error updating page ${page.id}:`, error.message);
            results.errors.push({
              pageId: page.id,
              prototypeId: page.prototype_id,
              error: error.message
            });
          }
        }
        
        offset += batchSize;
        
        // Add a small delay between batches to avoid overwhelming the database
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      results.endTime = new Date();
      const duration = (results.endTime - results.startTime) / 1000;
      
      console.log(`✅ [ResponsiveUpdateService] Update completed in ${duration}s`);
      console.log(`📊 Results: ${results.updatedPages} updated, ${results.skippedPages} skipped, ${results.errors.length} errors`);
      
      return results;
      
    } catch (error) {
      console.error('❌ [ResponsiveUpdateService] Fatal error during update:', error);
      results.endTime = new Date();
      results.errors.push({
        pageId: null,
        prototypeId: null,
        error: `Fatal error: ${error.message}`
      });
      throw error;
    }
  }
  
  /**
   * Update a single page with responsive design improvements
   * @param {Object} page - Page object from database
   * @param {boolean} dryRun - If true, only log what would be updated
   * @returns {Promise<Object>} Update result
   */
  async updateSinglePage(page, dryRun = false) {
    console.log(`🔍 Processing page ${page.id}: "${page.title}" (Prototype: ${page.prototype_title})`);
    
    const result = {
      pageId: page.id,
      updated: false,
      changes: [],
      originalSize: page.html_content?.length || 0,
      newSize: 0
    };
    
    try {
      // Check if page already has responsive improvements
      if (this.hasResponsiveFeatures(page.html_content)) {
        console.log(`⏭️  Page ${page.id} already has responsive features, skipping`);
        return result;
      }
      
      // Apply responsive improvements
      const improvedHtml = this.applyResponsiveImprovements(page.html_content);
      result.newSize = improvedHtml.length;
      
      // Detect what changes were made
      result.changes = this.detectChanges(page.html_content, improvedHtml);
      
      if (result.changes.length === 0) {
        console.log(`⏭️  No responsive improvements needed for page ${page.id}`);
        return result;
      }
      
      if (dryRun) {
        console.log(`🔍 [DRY RUN] Would update page ${page.id} with changes:`, result.changes);
        result.updated = true;
        return result;
      }
      
      // Update the page in database
      await prototypePageService.updatePage(page.id, page.user_id, {
        html_content: improvedHtml
      });
      
      console.log(`✅ Updated page ${page.id} with responsive improvements:`, result.changes);
      result.updated = true;
      
      return result;
      
    } catch (error) {
      console.error(`❌ Error processing page ${page.id}:`, error);
      throw error;
    }
  }
  
  /**
   * Check if HTML content already has responsive features
   * @param {string} htmlContent - HTML content to check
   * @returns {boolean} True if responsive features are present
   */
  hasResponsiveFeatures(htmlContent) {
    if (!htmlContent) return false;
    
    const responsiveIndicators = [
      'clamp(',
      'var(--space-',
      'var(--text-',
      'var(--touch-target',
      '@media (min-width:',
      'responsive-framework',
      'mobile-first'
    ];
    
    return responsiveIndicators.some(indicator => 
      htmlContent.toLowerCase().includes(indicator.toLowerCase())
    );
  }
  
  /**
   * Apply responsive improvements to HTML content
   * @param {string} htmlContent - Original HTML content
   * @returns {string} Improved HTML content
   */
  applyResponsiveImprovements(htmlContent) {
    if (!htmlContent) return htmlContent;
    
    let improvedHtml = htmlContent;
    
    // 1. Add responsive classes to common elements
    improvedHtml = this.addResponsiveClasses(improvedHtml);
    
    // 2. Improve touch targets
    improvedHtml = this.improveTouchTargets(improvedHtml);
    
    // 3. Add responsive grid layouts
    improvedHtml = this.addResponsiveGrids(improvedHtml);
    
    // 4. Improve navigation for mobile
    improvedHtml = this.improveMobileNavigation(improvedHtml);
    
    // 5. Add responsive typography
    improvedHtml = this.addResponsiveTypography(improvedHtml);
    
    return improvedHtml;
  }
  
  /**
   * Add responsive classes to common elements
   * @param {string} html - HTML content
   * @returns {string} HTML with responsive classes
   */
  addResponsiveClasses(html) {
    // Add responsive container classes
    html = html.replace(
      /<div([^>]*class="[^"]*container[^"]*"[^>]*)>/gi,
      '<div$1 style="max-width: 100%; margin: 0 auto; padding: 0 1rem;">'
    );
    
    // Make images responsive
    html = html.replace(
      /<img([^>]*)>/gi,
      '<img$1 style="max-width: 100%; height: auto; display: block;">'
    );
    
    // Add responsive flex layouts
    html = html.replace(
      /<div([^>]*class="[^"]*flex[^"]*"[^>]*)>/gi,
      (match, attrs) => {
        if (!attrs.includes('flex-col') && !attrs.includes('flex-row')) {
          return match.replace('flex', 'flex flex-col md:flex-row');
        }
        return match;
      }
    );
    
    return html;
  }
  
  /**
   * Improve touch targets for mobile devices
   * @param {string} html - HTML content
   * @returns {string} HTML with improved touch targets
   */
  improveTouchTargets(html) {
    // Improve button touch targets
    html = html.replace(
      /<button([^>]*)>/gi,
      '<button$1 style="min-height: 44px; min-width: 44px; padding: 0.5rem 1rem;">'
    );
    
    // Improve link touch targets
    html = html.replace(
      /<a([^>]*)>/gi,
      '<a$1 style="min-height: 44px; display: inline-flex; align-items: center; padding: 0.25rem 0;">'
    );
    
    return html;
  }
  
  /**
   * Add responsive grid layouts
   * @param {string} html - HTML content
   * @returns {string} HTML with responsive grids
   */
  addResponsiveGrids(html) {
    // Convert grid layouts to responsive
    html = html.replace(
      /<div([^>]*class="[^"]*grid[^"]*grid-cols-(\d+)[^"]*"[^>]*)>/gi,
      (match, attrs, cols) => {
        const responsiveGrid = `grid grid-cols-1 sm:grid-cols-2 md:grid-cols-${Math.min(cols, 3)} lg:grid-cols-${cols}`;
        return match.replace(/grid-cols-\d+/, responsiveGrid);
      }
    );
    
    return html;
  }
  
  /**
   * Improve navigation for mobile devices
   * @param {string} html - HTML content
   * @returns {string} HTML with improved mobile navigation
   */
  improveMobileNavigation(html) {
    // Add responsive navigation classes
    html = html.replace(
      /<nav([^>]*)>/gi,
      '<nav$1 style="padding: 1rem;">'
    );
    
    // Make navigation lists responsive
    html = html.replace(
      /<ul([^>]*class="[^"]*nav[^"]*"[^>]*)>/gi,
      '<ul$1 style="display: flex; flex-direction: column; gap: 0.5rem; list-style: none; margin: 0; padding: 0;"> @media (min-width: 768px) { ul { flex-direction: row; gap: 2rem; } }'
    );
    
    return html;
  }
  
  /**
   * Add responsive typography
   * @param {string} html - HTML content
   * @returns {string} HTML with responsive typography
   */
  addResponsiveTypography(html) {
    // Add responsive heading sizes
    const headingMap = {
      'h1': 'clamp(2.25rem, 1.9rem + 1.75vw, 3rem)',
      'h2': 'clamp(1.875rem, 1.6rem + 1.375vw, 2.5rem)',
      'h3': 'clamp(1.5rem, 1.3rem + 1vw, 2rem)',
      'h4': 'clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem)',
      'h5': 'clamp(1.125rem, 1rem + 0.625vw, 1.25rem)',
      'h6': 'clamp(1rem, 0.9rem + 0.5vw, 1.125rem)'
    };
    
    Object.entries(headingMap).forEach(([tag, size]) => {
      const regex = new RegExp(`<${tag}([^>]*)>`, 'gi');
      html = html.replace(regex, `<${tag}$1 style="font-size: ${size};">`);
    });
    
    return html;
  }
  
  /**
   * Detect what changes were made to the HTML
   * @param {string} original - Original HTML
   * @param {string} improved - Improved HTML
   * @returns {Array} List of changes made
   */
  detectChanges(original, improved) {
    const changes = [];
    
    if (improved.includes('min-height: 44px')) {
      changes.push('Added touch-friendly button sizing');
    }
    
    if (improved.includes('max-width: 100%')) {
      changes.push('Made images responsive');
    }
    
    if (improved.includes('clamp(')) {
      changes.push('Added responsive typography');
    }
    
    if (improved.includes('flex-col md:flex-row')) {
      changes.push('Added responsive flex layouts');
    }
    
    if (improved.includes('grid-cols-1 sm:grid-cols-2')) {
      changes.push('Added responsive grid layouts');
    }
    
    if (original.length !== improved.length) {
      changes.push(`Content size changed: ${original.length} → ${improved.length} chars`);
    }
    
    return changes;
  }
  
  /**
   * Get update statistics for all pages
   * @returns {Promise<Object>} Statistics about pages and their responsive status
   */
  async getUpdateStatistics() {
    try {
      const stats = {
        totalPages: 0,
        responsivePages: 0,
        nonResponsivePages: 0,
        totalPrototypes: 0,
        pagesByPrototype: {}
      };
      
      // Get all pages with prototype info
      const query = `
        SELECT pp.*, p.title as prototype_title, p.user_id
        FROM prototype_pages pp
        JOIN prototypes p ON pp.prototype_id = p.id
        ORDER BY p.id, pp.page_order
      `;
      
      const result = await pool.query(query);
      const pages = result.rows;
      
      stats.totalPages = pages.length;
      
      // Count prototypes
      const prototypeIds = new Set(pages.map(p => p.prototype_id));
      stats.totalPrototypes = prototypeIds.size;
      
      // Analyze each page
      for (const page of pages) {
        const isResponsive = this.hasResponsiveFeatures(page.html_content);
        
        if (isResponsive) {
          stats.responsivePages++;
        } else {
          stats.nonResponsivePages++;
        }
        
        // Group by prototype
        if (!stats.pagesByPrototype[page.prototype_id]) {
          stats.pagesByPrototype[page.prototype_id] = {
            title: page.prototype_title,
            totalPages: 0,
            responsivePages: 0,
            nonResponsivePages: 0
          };
        }
        
        stats.pagesByPrototype[page.prototype_id].totalPages++;
        if (isResponsive) {
          stats.pagesByPrototype[page.prototype_id].responsivePages++;
        } else {
          stats.pagesByPrototype[page.prototype_id].nonResponsivePages++;
        }
      }
      
      return stats;
      
    } catch (error) {
      console.error('❌ Error getting update statistics:', error);
      throw error;
    }
  }
}

module.exports = new ResponsiveUpdateService();
