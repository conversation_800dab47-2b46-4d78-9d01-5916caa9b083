import { useEffect, useState, useRef } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { FiArrowLeft, FiEdit2, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Share2, FiZap } from 'react-icons/fi';
import styles from './PrototypeViewerPage.module.css';
import { getPrototypeById, Prototype } from '../services/prototypeService';
import ShareButton from '../components/ShareButton';

// Import responsive framework
const RESPONSIVE_FRAMEWORK_CSS = `
/* ===== RESPONSIVE FRAMEWORK - MOBILE FIRST ===== */

/* CSS Custom Properties for Responsive Design */
:root {
  /* Responsive Breakpoints */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  /* Responsive Typography Scale */
  --text-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
  --text-sm: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
  --text-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
  --text-lg: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);
  --text-xl: clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem);
  --text-2xl: clamp(1.5rem, 1.3rem + 1vw, 2rem);
  --text-3xl: clamp(1.875rem, 1.6rem + 1.375vw, 2.5rem);
  --text-4xl: clamp(2.25rem, 1.9rem + 1.75vw, 3rem);
  --text-5xl: clamp(3rem, 2.5rem + 2.5vw, 4rem);

  /* Responsive Spacing Scale */
  --space-xs: clamp(0.25rem, 0.2rem + 0.25vw, 0.375rem);
  --space-sm: clamp(0.5rem, 0.4rem + 0.5vw, 0.75rem);
  --space-md: clamp(1rem, 0.8rem + 1vw, 1.5rem);
  --space-lg: clamp(1.5rem, 1.2rem + 1.5vw, 2.25rem);
  --space-xl: clamp(2rem, 1.6rem + 2vw, 3rem);
  --space-2xl: clamp(3rem, 2.4rem + 3vw, 4.5rem);
  --space-3xl: clamp(4rem, 3.2rem + 4vw, 6rem);

  /* Touch Target Sizes */
  --touch-target-min: 44px;
  --touch-target-comfortable: 48px;
  --touch-target-large: 56px;

  /* Container Sizes */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;

  /* Modern Color System */
  --color-primary: #3b82f6;
  --color-primary-light: #60a5fa;
  --color-primary-dark: #1d4ed8;
  --color-secondary: #10b981;
  --color-accent: #f59e0b;
  --color-neutral-50: #f9fafb;
  --color-neutral-100: #f3f4f6;
  --color-neutral-200: #e5e7eb;
  --color-neutral-300: #d1d5db;
  --color-neutral-400: #9ca3af;
  --color-neutral-500: #6b7280;
  --color-neutral-600: #4b5563;
  --color-neutral-700: #374151;
  --color-neutral-800: #1f2937;
  --color-neutral-900: #111827;

  /* Animation Variables */
  --transition-fast: 150ms ease-out;
  --transition-normal: 250ms ease-out;
  --transition-slow: 350ms ease-out;
  --animation-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* ===== RESPONSIVE BASE STYLES ===== */

/* Enhanced Box Sizing */
*, *::before, *::after {
  box-sizing: border-box;
}

/* Mobile-First Body Styles */
body {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: var(--text-base);
  line-height: 1.6;
  color: var(--color-neutral-800);
  background-color: var(--color-neutral-50);
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-text-size-adjust: 100%;
}

/* Responsive Images */
img, picture, video, canvas, svg {
  display: block;
  max-width: 100%;
  height: auto;
}

/* ===== RESPONSIVE TYPOGRAPHY ===== */

h1, h2, h3, h4, h5, h6 {
  margin: 0 0 var(--space-md) 0;
  font-weight: 600;
  line-height: 1.2;
  color: var(--color-neutral-900);
}

h1 { font-size: var(--text-4xl); }
h2 { font-size: var(--text-3xl); }
h3 { font-size: var(--text-2xl); }
h4 { font-size: var(--text-xl); }
h5 { font-size: var(--text-lg); }
h6 { font-size: var(--text-base); }

p {
  margin: 0 0 var(--space-md) 0;
  font-size: var(--text-base);
}

/* ===== TOUCH-FRIENDLY COMPONENTS ===== */

/* Enhanced Button Styles */
button, .btn, [role="button"] {
  min-height: var(--touch-target-min);
  min-width: var(--touch-target-min);
  padding: var(--space-sm) var(--space-md);
  border: none;
  border-radius: 8px;
  font-size: var(--text-base);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-xs);
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

/* Enhanced Form Controls */
input, textarea, select {
  min-height: var(--touch-target-min);
  padding: var(--space-sm) var(--space-md);
  border: 2px solid var(--color-neutral-200);
  border-radius: 8px;
  font-size: var(--text-base);
  font-family: inherit;
  transition: all var(--transition-normal);
  width: 100%;
  background-color: white;
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Enhanced Links */
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-normal);
  min-height: var(--touch-target-min);
  display: inline-flex;
  align-items: center;
  padding: var(--space-xs) 0;
}

/* Accessibility Enhancements */
*:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
}
`;

// Returns the full HTML for the iframe, using a template approach
function getIframeHtml(prototype: Prototype): string {
  try {
    // Create a safe version of the content with scripts properly tagged
    const processedContent = prototype.html.replace(/<script>/g, '<script data-exec="inline">');

    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover, user-scalable=no">
      <meta name="theme-color" content="#3b82f6">
      <meta name="color-scheme" content="light">
      <title>${prototype.title}</title>
      <script src="https://cdn.tailwindcss.com/3.4.16"></script>
      <script>tailwind.config={theme:{extend:{colors:{primary:'#3b82f6',secondary:'#10b981'},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
      <link rel="preconnect" href="https://fonts.googleapis.com">
      <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
      <link rel="dns-prefetch" href="https://cdn.tailwindcss.com">
      <link rel="stylesheet" crossorigin href="https://static.readdy.ai/static/index-DXIoEVCZ.css">
      <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
      <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
      <link href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css" rel="stylesheet">
      <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.5.0/echarts.min.js"></script>
      <style>
        ${RESPONSIVE_FRAMEWORK_CSS}
      </style>
      <script>
        // This will execute all inline scripts after DOMContentLoaded (needed for charts)
        document.addEventListener('DOMContentLoaded', function() {
          var scripts = document.querySelectorAll('script[data-exec="inline"]');
          scripts.forEach(function(script) {
            try {
              // eslint-disable-next-line no-eval
              eval(script.textContent);
            } catch (e) {
              console.error('Error executing inline script:', e);
            }
          });
        });
      </script>
      <style>
      ${prototype.css || ''}
      :where([class^="ri-"])::before { content: "\\f3c2"; }
      body {
        font-family: 'Inter', sans-serif;
        background-color: #f9fafb;
      }
      </style>
    </head>
    <body>
      ${processedContent}
    </body>
    </html>
    `;
  } catch (error) {
    console.error('Error in getIframeHtml:', error);

    // Return a fallback HTML with error message
    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Error</title>
      <style>
        body { font-family: sans-serif; padding: 20px; color: #333; }
        .error { color: #e53e3e; border: 1px solid #e53e3e; padding: 15px; border-radius: 5px; }
      </style>
    </head>
    <body>
      <div class="error">
        <h2>Error rendering content</h2>
        <p>There was an error processing the content. Please try again.</p>
        <p>Error details: ${error instanceof Error ? error.message : 'Unknown error'}</p>
      </div>
    </body>
    </html>
    `;
  }
}

export function PrototypeViewerPage() {
  const { id } = useParams<{ id: string }>();
  const [prototype, setPrototype] = useState<Prototype | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    if (!id) return;

    const fetchPrototype = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await getPrototypeById(parseInt(id, 10));
        setPrototype(data);
      } catch (err: any) {
        console.error('Error fetching prototype:', err);
        setError('Failed to load prototype. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchPrototype();
  }, [id]);

  // Update iframe when prototype changes
  useEffect(() => {
    if (prototype && iframeRef.current) {
      iframeRef.current.srcdoc = getIframeHtml(prototype);
    }
  }, [prototype]);

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <FiLoader className={styles.loadingIcon} />
        <span>Loading prototype...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.errorContainer}>
        <div className={styles.error}>{error}</div>
        <Link to="/prototypes" className={styles.backLink}>
          <FiArrowLeft />
          Back to My Prototypes
        </Link>
      </div>
    );
  }

  if (!prototype) {
    return (
      <div className={styles.errorContainer}>
        <div className={styles.error}>Prototype not found</div>
        <Link to="/prototypes" className={styles.backLink}>
          <FiArrowLeft />
          Back to My Prototypes
        </Link>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.headerLeft}>
          <Link to="/prototypes" className={styles.backLink}>
            <FiArrowLeft />
            Back
          </Link>
          <h1 className={styles.title}>{prototype.title}</h1>
        </div>
        <div className={styles.headerRight}>
          <ShareButton
            prototypeId={prototype.id.toString()}
            prototypeName={prototype.title}
            className={styles.shareButton}
          />
          <Link to={`/editor-v3-refactored/${prototype.id}`} className={styles.editButton} style={{ backgroundColor: '#ff6b35', borderColor: '#ff6b35' }}>
            <FiEdit2 />
            Edit
          </Link>
        </div>
      </div>

      <div className={styles.previewContainer}>
        <iframe
          ref={iframeRef}
          title={prototype.title}
          className={styles.previewFrame}
          sandbox="allow-scripts allow-same-origin allow-popups allow-forms"
        />
      </div>
    </div>
  );
}
