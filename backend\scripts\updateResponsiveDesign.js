#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> Script to Update All Pages with Responsive Design
 * 
 * This script updates all existing pages in the database with responsive design improvements.
 * 
 * Usage:
 *   node scripts/updateResponsiveDesign.js [options]
 * 
 * Options:
 *   --dry-run    Run in dry-run mode (show what would be updated without making changes)
 *   --batch-size Number of pages to process in each batch (default: 50)
 *   --stats      Show statistics about current responsive status
 *   --help       Show this help message
 */

const responsiveUpdateService = require('../services/responsiveUpdateService');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  dryRun: args.includes('--dry-run'),
  batchSize: parseInt(args.find(arg => arg.startsWith('--batch-size='))?.split('=')[1]) || 50,
  showStats: args.includes('--stats'),
  showHelp: args.includes('--help')
};

// Help message
const showHelp = () => {
  console.log(`
📱 Responsive Design Update Tool

This tool updates all existing pages in the database with responsive design improvements.

Usage:
  node scripts/updateResponsiveDesign.js [options]

Options:
  --dry-run           Run in dry-run mode (show what would be updated without making changes)
  --batch-size=N      Number of pages to process in each batch (default: 50)
  --stats             Show statistics about current responsive status
  --help              Show this help message

Examples:
  node scripts/updateResponsiveDesign.js --stats
  node scripts/updateResponsiveDesign.js --dry-run
  node scripts/updateResponsiveDesign.js --batch-size=25
  node scripts/updateResponsiveDesign.js

Features Added:
  ✅ Mobile-first responsive design
  ✅ Touch-friendly button and link sizing (44px minimum)
  ✅ Responsive typography with clamp() functions
  ✅ Responsive grid layouts that adapt to screen size
  ✅ Mobile-optimized navigation patterns
  ✅ Responsive images and containers
  ✅ Accessibility improvements
  ✅ Smooth animations with reduced motion support
`);
};

// Show statistics
const showStatistics = async () => {
  try {
    console.log('📊 Gathering responsive design statistics...\n');
    
    const stats = await responsiveUpdateService.getUpdateStatistics();
    
    console.log('='.repeat(60));
    console.log('📱 RESPONSIVE DESIGN STATISTICS');
    console.log('='.repeat(60));
    console.log(`📄 Total Pages: ${stats.totalPages}`);
    console.log(`📱 Responsive Pages: ${stats.responsivePages} (${((stats.responsivePages / stats.totalPages) * 100).toFixed(1)}%)`);
    console.log(`🔧 Non-Responsive Pages: ${stats.nonResponsivePages} (${((stats.nonResponsivePages / stats.totalPages) * 100).toFixed(1)}%)`);
    console.log(`🗂️  Total Prototypes: ${stats.totalPrototypes}`);
    console.log('');
    
    if (Object.keys(stats.pagesByPrototype).length > 0) {
      console.log('📋 BREAKDOWN BY PROTOTYPE:');
      console.log('-'.repeat(60));
      
      Object.entries(stats.pagesByPrototype).forEach(([prototypeId, prototypeStats]) => {
        const responsivePercent = ((prototypeStats.responsivePages / prototypeStats.totalPages) * 100).toFixed(1);
        console.log(`📁 ${prototypeStats.title}`);
        console.log(`   📄 Pages: ${prototypeStats.totalPages} | 📱 Responsive: ${prototypeStats.responsivePages} (${responsivePercent}%) | 🔧 Need Update: ${prototypeStats.nonResponsivePages}`);
        console.log('');
      });
    }
    
    if (stats.nonResponsivePages > 0) {
      console.log('💡 RECOMMENDATION:');
      console.log(`   Run: node scripts/updateResponsiveDesign.js --dry-run`);
      console.log(`   Then: node scripts/updateResponsiveDesign.js`);
    } else {
      console.log('✅ All pages are already responsive!');
    }
    
    console.log('='.repeat(60));
    
  } catch (error) {
    console.error('❌ Error getting statistics:', error.message);
    process.exit(1);
  }
};

// Main update function
const runUpdate = async () => {
  try {
    console.log('🚀 Starting responsive design update...\n');
    
    if (options.dryRun) {
      console.log('🔍 DRY RUN MODE - No changes will be made\n');
    }
    
    const results = await responsiveUpdateService.updateAllPages({
      dryRun: options.dryRun,
      batchSize: options.batchSize
    });
    
    // Display results
    console.log('\n' + '='.repeat(60));
    console.log('📊 UPDATE RESULTS');
    console.log('='.repeat(60));
    console.log(`📄 Total Pages Processed: ${results.totalPages}`);
    console.log(`✅ Pages Updated: ${results.updatedPages}`);
    console.log(`⏭️  Pages Skipped: ${results.skippedPages}`);
    console.log(`❌ Errors: ${results.errors.length}`);
    
    const duration = (results.endTime - results.startTime) / 1000;
    console.log(`⏱️  Duration: ${duration.toFixed(2)} seconds`);
    
    if (results.errors.length > 0) {
      console.log('\n❌ ERRORS:');
      results.errors.forEach((error, index) => {
        console.log(`${index + 1}. Page ${error.pageId || 'N/A'} (Prototype ${error.prototypeId || 'N/A'}): ${error.error}`);
      });
    }
    
    if (options.dryRun && results.updatedPages > 0) {
      console.log('\n💡 To apply these changes, run without --dry-run flag:');
      console.log('   node scripts/updateResponsiveDesign.js');
    }
    
    if (!options.dryRun && results.updatedPages > 0) {
      console.log('\n✅ Responsive design updates completed successfully!');
      console.log('📱 All updated pages now include:');
      console.log('   • Mobile-first responsive design');
      console.log('   • Touch-friendly interface elements');
      console.log('   • Responsive typography and spacing');
      console.log('   • Adaptive grid layouts');
      console.log('   • Mobile-optimized navigation');
      console.log('   • Accessibility improvements');
    }
    
    console.log('='.repeat(60));
    
  } catch (error) {
    console.error('❌ Fatal error during update:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
};

// Main execution
const main = async () => {
  console.log('📱 JustPrototype Responsive Design Update Tool\n');
  
  if (options.showHelp) {
    showHelp();
    return;
  }
  
  if (options.showStats) {
    await showStatistics();
    return;
  }
  
  // Confirm before running live update
  if (!options.dryRun) {
    console.log('⚠️  WARNING: This will modify all pages in the database.');
    console.log('💡 Consider running with --dry-run first to preview changes.\n');
    
    // In a real environment, you might want to add a confirmation prompt
    // For now, we'll proceed with a warning
    console.log('🚀 Proceeding with live update in 3 seconds...');
    await new Promise(resolve => setTimeout(resolve, 3000));
  }
  
  await runUpdate();
};

// Handle uncaught errors
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  process.exit(1);
});

// Run the script
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Script failed:', error.message);
    process.exit(1);
  });
}

module.exports = { main, showStatistics, runUpdate };
