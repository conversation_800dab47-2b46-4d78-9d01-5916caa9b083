#!/usr/bin/env node

/**
 * Responsive Design Testing Script
 * 
 * This script tests the responsive design implementation across different
 * device breakpoints and verifies accessibility standards.
 */

const responsiveUpdateService = require('../services/responsiveUpdateService');
const prototypePageService = require('../services/prototypePageService');
const prototypeService = require('../services/prototypeService');

// Test breakpoints (in pixels)
const BREAKPOINTS = {
  mobile: 320,
  mobileLarge: 480,
  tablet: 768,
  desktop: 1024,
  desktopLarge: 1440,
  desktopXL: 1920
};

// Responsive design criteria
const RESPONSIVE_CRITERIA = {
  touchTargets: {
    minSize: 44, // pixels
    description: 'Touch targets should be at least 44px for accessibility'
  },
  typography: {
    minFontSize: 14, // pixels
    description: 'Text should be readable at minimum 14px'
  },
  images: {
    maxWidth: '100%',
    description: 'Images should be responsive with max-width: 100%'
  },
  containers: {
    maxWidth: '100%',
    description: 'Containers should not exceed viewport width'
  }
};

class ResponsiveDesignTester {
  
  /**
   * Run comprehensive responsive design tests
   */
  async runTests() {
    console.log('🧪 Starting Responsive Design Tests\n');
    
    const results = {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      warnings: 0,
      testResults: [],
      summary: {}
    };

    try {
      // Test 1: Check responsive framework implementation
      console.log('📋 Test 1: Responsive Framework Implementation');
      const frameworkTest = await this.testResponsiveFramework();
      results.testResults.push(frameworkTest);
      this.updateResults(results, frameworkTest);

      // Test 2: Test page content responsiveness
      console.log('\n📋 Test 2: Page Content Responsiveness');
      const contentTest = await this.testPageContentResponsiveness();
      results.testResults.push(contentTest);
      this.updateResults(results, contentTest);

      // Test 3: Test touch target sizes
      console.log('\n📋 Test 3: Touch Target Accessibility');
      const touchTest = await this.testTouchTargets();
      results.testResults.push(touchTest);
      this.updateResults(results, touchTest);

      // Test 4: Test viewport and meta tags
      console.log('\n📋 Test 4: Viewport and Meta Tags');
      const viewportTest = await this.testViewportConfiguration();
      results.testResults.push(viewportTest);
      this.updateResults(results, viewportTest);

      // Test 5: Test mobile navigation
      console.log('\n📋 Test 5: Mobile Navigation Features');
      const navTest = await this.testMobileNavigation();
      results.testResults.push(navTest);
      this.updateResults(results, navTest);

      // Test 6: Test performance implications
      console.log('\n📋 Test 6: Performance Impact');
      const perfTest = await this.testPerformanceImpact();
      results.testResults.push(perfTest);
      this.updateResults(results, perfTest);

      // Generate summary
      results.summary = this.generateSummary(results);
      
      // Display results
      this.displayResults(results);
      
      return results;
      
    } catch (error) {
      console.error('❌ Error running responsive design tests:', error);
      throw error;
    }
  }

  /**
   * Test responsive framework implementation
   */
  async testResponsiveFramework() {
    const test = {
      name: 'Responsive Framework Implementation',
      status: 'passed',
      details: [],
      warnings: [],
      errors: []
    };

    try {
      // Check if responsive framework files exist
      const fs = require('fs');
      const path = require('path');
      
      const frameworkPath = path.join(__dirname, '../config/responsiveFramework.js');
      if (!fs.existsSync(frameworkPath)) {
        test.errors.push('Responsive framework file not found');
        test.status = 'failed';
      } else {
        test.details.push('✅ Responsive framework file exists');
      }

      // Check framework content
      const framework = require('../config/responsiveFramework');
      if (!framework.COMPLETE_RESPONSIVE_FRAMEWORK) {
        test.errors.push('Complete responsive framework not exported');
        test.status = 'failed';
      } else {
        test.details.push('✅ Complete responsive framework available');
      }

      // Check for key responsive features
      const frameworkContent = framework.COMPLETE_RESPONSIVE_FRAMEWORK || '';
      const requiredFeatures = [
        'clamp(',
        'var(--touch-target',
        '@media (min-width:',
        'mobile-first',
        'responsive'
      ];

      requiredFeatures.forEach(feature => {
        if (frameworkContent.includes(feature)) {
          test.details.push(`✅ Contains ${feature}`);
        } else {
          test.warnings.push(`⚠️  Missing ${feature}`);
        }
      });

    } catch (error) {
      test.errors.push(`Error testing framework: ${error.message}`);
      test.status = 'failed';
    }

    return test;
  }

  /**
   * Test page content responsiveness
   */
  async testPageContentResponsiveness() {
    const test = {
      name: 'Page Content Responsiveness',
      status: 'passed',
      details: [],
      warnings: [],
      errors: []
    };

    try {
      // Get statistics about responsive pages
      const stats = await responsiveUpdateService.getUpdateStatistics();
      
      test.details.push(`📊 Total pages: ${stats.totalPages}`);
      test.details.push(`📱 Responsive pages: ${stats.responsivePages}`);
      test.details.push(`🔧 Non-responsive pages: ${stats.nonResponsivePages}`);

      const responsivePercentage = stats.totalPages > 0 ? 
        (stats.responsivePages / stats.totalPages) * 100 : 0;

      if (responsivePercentage >= 90) {
        test.details.push(`✅ ${responsivePercentage.toFixed(1)}% of pages are responsive`);
      } else if (responsivePercentage >= 70) {
        test.warnings.push(`⚠️  Only ${responsivePercentage.toFixed(1)}% of pages are responsive`);
      } else {
        test.errors.push(`❌ Only ${responsivePercentage.toFixed(1)}% of pages are responsive`);
        test.status = 'failed';
      }

      // Test a sample of pages
      if (stats.totalPages > 0) {
        const sampleSize = Math.min(5, stats.totalPages);
        test.details.push(`🔍 Testing ${sampleSize} sample pages...`);
        
        // This would require a headless browser to properly test
        // For now, we'll check the HTML content for responsive indicators
        test.details.push('📝 Note: Full responsive testing requires browser automation');
      }

    } catch (error) {
      test.errors.push(`Error testing page responsiveness: ${error.message}`);
      test.status = 'failed';
    }

    return test;
  }

  /**
   * Test touch target sizes
   */
  async testTouchTargets() {
    const test = {
      name: 'Touch Target Accessibility',
      status: 'passed',
      details: [],
      warnings: [],
      errors: []
    };

    try {
      // Check if touch target CSS variables are defined
      const framework = require('../config/responsiveFramework');
      const frameworkContent = framework.COMPLETE_RESPONSIVE_FRAMEWORK || '';

      if (frameworkContent.includes('--touch-target-min: 44px')) {
        test.details.push('✅ Minimum touch target size defined (44px)');
      } else {
        test.errors.push('❌ Minimum touch target size not defined');
        test.status = 'failed';
      }

      if (frameworkContent.includes('min-height: var(--touch-target-min)')) {
        test.details.push('✅ Touch target sizing applied to interactive elements');
      } else {
        test.warnings.push('⚠️  Touch target sizing may not be applied consistently');
      }

      // Check for touch-friendly styles
      const touchFeatures = [
        'touch-action: manipulation',
        'min-height: 44px',
        'min-width: 44px'
      ];

      touchFeatures.forEach(feature => {
        if (frameworkContent.includes(feature)) {
          test.details.push(`✅ Includes ${feature}`);
        } else {
          test.warnings.push(`⚠️  Missing ${feature}`);
        }
      });

    } catch (error) {
      test.errors.push(`Error testing touch targets: ${error.message}`);
      test.status = 'failed';
    }

    return test;
  }

  /**
   * Test viewport configuration
   */
  async testViewportConfiguration() {
    const test = {
      name: 'Viewport and Meta Tags',
      status: 'passed',
      details: [],
      warnings: [],
      errors: []
    };

    try {
      // Check if enhanced viewport meta is defined
      const framework = require('../config/responsiveFramework');
      
      if (framework.ENHANCED_VIEWPORT_META) {
        test.details.push('✅ Enhanced viewport meta tag defined');
        
        const viewportContent = framework.ENHANCED_VIEWPORT_META;
        const requiredViewportFeatures = [
          'width=device-width',
          'initial-scale=1.0',
          'viewport-fit=cover'
        ];

        requiredViewportFeatures.forEach(feature => {
          if (viewportContent.includes(feature)) {
            test.details.push(`✅ Viewport includes ${feature}`);
          } else {
            test.warnings.push(`⚠️  Viewport missing ${feature}`);
          }
        });
      } else {
        test.errors.push('❌ Enhanced viewport meta tag not defined');
        test.status = 'failed';
      }

      // Check performance meta tags
      if (framework.PERFORMANCE_META_TAGS) {
        test.details.push('✅ Performance meta tags defined');
      } else {
        test.warnings.push('⚠️  Performance meta tags not defined');
      }

    } catch (error) {
      test.errors.push(`Error testing viewport configuration: ${error.message}`);
      test.status = 'failed';
    }

    return test;
  }

  /**
   * Test mobile navigation features
   */
  async testMobileNavigation() {
    const test = {
      name: 'Mobile Navigation Features',
      status: 'passed',
      details: [],
      warnings: [],
      errors: []
    };

    try {
      // Check if mobile utilities exist
      const fs = require('fs');
      const path = require('path');
      
      const mobileUtilsPath = path.join(__dirname, '../../ui/src/utils/mobileUtils.ts');
      if (fs.existsSync(mobileUtilsPath)) {
        test.details.push('✅ Mobile utilities module exists');
      } else {
        test.errors.push('❌ Mobile utilities module not found');
        test.status = 'failed';
      }

      // Check if mobile wrapper component exists
      const mobileWrapperPath = path.join(__dirname, '../../ui/src/components/MobileOptimizedWrapper.tsx');
      if (fs.existsSync(mobileWrapperPath)) {
        test.details.push('✅ Mobile optimized wrapper component exists');
      } else {
        test.warnings.push('⚠️  Mobile optimized wrapper component not found');
      }

      // Check framework for mobile navigation styles
      const framework = require('../config/responsiveFramework');
      const frameworkContent = framework.COMPLETE_RESPONSIVE_FRAMEWORK || '';

      const mobileNavFeatures = [
        'mobile-nav',
        'touch-action',
        'swipe',
        '@media (max-width:'
      ];

      mobileNavFeatures.forEach(feature => {
        if (frameworkContent.includes(feature)) {
          test.details.push(`✅ Includes ${feature} support`);
        } else {
          test.warnings.push(`⚠️  Missing ${feature} support`);
        }
      });

    } catch (error) {
      test.errors.push(`Error testing mobile navigation: ${error.message}`);
      test.status = 'failed';
    }

    return test;
  }

  /**
   * Test performance impact
   */
  async testPerformanceImpact() {
    const test = {
      name: 'Performance Impact',
      status: 'passed',
      details: [],
      warnings: [],
      errors: []
    };

    try {
      // Check framework size
      const framework = require('../config/responsiveFramework');
      const frameworkSize = framework.COMPLETE_RESPONSIVE_FRAMEWORK?.length || 0;
      
      test.details.push(`📏 Framework size: ${frameworkSize} characters`);
      
      if (frameworkSize < 50000) { // 50KB
        test.details.push('✅ Framework size is reasonable');
      } else {
        test.warnings.push('⚠️  Framework size is large, may impact performance');
      }

      // Check for performance optimizations
      const frameworkContent = framework.COMPLETE_RESPONSIVE_FRAMEWORK || '';
      
      const performanceFeatures = [
        'prefers-reduced-motion',
        'will-change',
        'transform',
        'transition'
      ];

      performanceFeatures.forEach(feature => {
        if (frameworkContent.includes(feature)) {
          test.details.push(`✅ Includes ${feature} optimization`);
        }
      });

      // Check for accessibility features
      const accessibilityFeatures = [
        'prefers-contrast',
        'focus',
        'outline',
        'aria-'
      ];

      accessibilityFeatures.forEach(feature => {
        if (frameworkContent.includes(feature)) {
          test.details.push(`✅ Includes ${feature} accessibility feature`);
        }
      });

    } catch (error) {
      test.errors.push(`Error testing performance impact: ${error.message}`);
      test.status = 'failed';
    }

    return test;
  }

  /**
   * Update test results
   */
  updateResults(results, test) {
    results.totalTests++;
    
    if (test.status === 'passed') {
      results.passedTests++;
    } else {
      results.failedTests++;
    }
    
    results.warnings += test.warnings.length;
  }

  /**
   * Generate test summary
   */
  generateSummary(results) {
    const passRate = (results.passedTests / results.totalTests) * 100;
    
    return {
      passRate: passRate.toFixed(1),
      grade: passRate >= 90 ? 'A' : passRate >= 80 ? 'B' : passRate >= 70 ? 'C' : 'F',
      recommendation: this.getRecommendation(passRate, results.warnings)
    };
  }

  /**
   * Get recommendation based on test results
   */
  getRecommendation(passRate, warningCount) {
    if (passRate >= 90 && warningCount === 0) {
      return 'Excellent! Responsive design implementation is complete and optimized.';
    } else if (passRate >= 80) {
      return 'Good responsive design implementation. Address warnings for optimal experience.';
    } else if (passRate >= 70) {
      return 'Responsive design partially implemented. Several improvements needed.';
    } else {
      return 'Responsive design implementation needs significant work. Run update script.';
    }
  }

  /**
   * Display test results
   */
  displayResults(results) {
    console.log('\n' + '='.repeat(80));
    console.log('📱 RESPONSIVE DESIGN TEST RESULTS');
    console.log('='.repeat(80));
    
    results.testResults.forEach((test, index) => {
      console.log(`\n${index + 1}. ${test.name}: ${test.status.toUpperCase()}`);
      
      if (test.details.length > 0) {
        test.details.forEach(detail => console.log(`   ${detail}`));
      }
      
      if (test.warnings.length > 0) {
        test.warnings.forEach(warning => console.log(`   ${warning}`));
      }
      
      if (test.errors.length > 0) {
        test.errors.forEach(error => console.log(`   ${error}`));
      }
    });

    console.log('\n' + '-'.repeat(80));
    console.log('📊 SUMMARY');
    console.log('-'.repeat(80));
    console.log(`Tests Passed: ${results.passedTests}/${results.totalTests}`);
    console.log(`Pass Rate: ${results.summary.passRate}%`);
    console.log(`Grade: ${results.summary.grade}`);
    console.log(`Warnings: ${results.warnings}`);
    console.log(`\n💡 Recommendation: ${results.summary.recommendation}`);
    console.log('='.repeat(80));
  }
}

// Main execution
const main = async () => {
  const tester = new ResponsiveDesignTester();
  
  try {
    const results = await tester.runTests();
    
    // Exit with appropriate code
    if (results.failedTests > 0) {
      process.exit(1);
    } else if (results.warnings > 0) {
      process.exit(2); // Warning exit code
    } else {
      process.exit(0);
    }
    
  } catch (error) {
    console.error('❌ Test execution failed:', error.message);
    process.exit(1);
  }
};

if (require.main === module) {
  main();
}

module.exports = { ResponsiveDesignTester };
