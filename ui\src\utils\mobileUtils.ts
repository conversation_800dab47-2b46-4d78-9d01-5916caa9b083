/**
 * Mobile Utilities for JustPrototype
 * 
 * This module provides utilities for mobile-specific functionality including
 * touch gestures, device detection, and mobile-optimized interactions.
 */

export interface TouchGestureOptions {
  minSwipeDistance?: number;
  maxSwipeTime?: number;
  preventDefaultScroll?: boolean;
}

export interface SwipeEvent {
  direction: 'left' | 'right' | 'up' | 'down';
  distance: number;
  duration: number;
  startX: number;
  startY: number;
  endX: number;
  endY: number;
}

/**
 * Device detection utilities
 */
export const DeviceUtils = {
  /**
   * Check if the current device is mobile
   */
  isMobile(): boolean {
    return window.innerWidth < 768;
  },

  /**
   * Check if the current device is tablet
   */
  isTablet(): boolean {
    return window.innerWidth >= 768 && window.innerWidth < 1024;
  },

  /**
   * Check if the current device is desktop
   */
  isDesktop(): boolean {
    return window.innerWidth >= 1024;
  },

  /**
   * Check if the device supports touch
   */
  isTouchDevice(): boolean {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  },

  /**
   * Get the current viewport size
   */
  getViewportSize(): { width: number; height: number } {
    return {
      width: window.innerWidth,
      height: window.innerHeight
    };
  },

  /**
   * Check if the device is in landscape mode
   */
  isLandscape(): boolean {
    return window.innerWidth > window.innerHeight;
  },

  /**
   * Check if the device is in portrait mode
   */
  isPortrait(): boolean {
    return window.innerHeight > window.innerWidth;
  }
};

/**
 * Touch gesture handler class
 */
export class TouchGestureHandler {
  private element: HTMLElement;
  private options: TouchGestureOptions;
  private touchStartX = 0;
  private touchStartY = 0;
  private touchStartTime = 0;
  private onSwipe?: (event: SwipeEvent) => void;
  private onTap?: (event: TouchEvent) => void;
  private onLongPress?: (event: TouchEvent) => void;
  private longPressTimer?: number;

  constructor(element: HTMLElement, options: TouchGestureOptions = {}) {
    this.element = element;
    this.options = {
      minSwipeDistance: 50,
      maxSwipeTime: 1000,
      preventDefaultScroll: false,
      ...options
    };

    this.bindEvents();
  }

  private bindEvents(): void {
    this.element.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: !this.options.preventDefaultScroll });
    this.element.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: !this.options.preventDefaultScroll });
    this.element.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: !this.options.preventDefaultScroll });
  }

  private handleTouchStart(e: TouchEvent): void {
    const touch = e.touches[0];
    this.touchStartX = touch.clientX;
    this.touchStartY = touch.clientY;
    this.touchStartTime = Date.now();

    // Start long press timer
    this.longPressTimer = window.setTimeout(() => {
      if (this.onLongPress) {
        this.onLongPress(e);
      }
    }, 500);
  }

  private handleTouchMove(e: TouchEvent): void {
    // Clear long press timer on move
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = undefined;
    }

    if (this.options.preventDefaultScroll) {
      e.preventDefault();
    }
  }

  private handleTouchEnd(e: TouchEvent): void {
    // Clear long press timer
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = undefined;
    }

    const touch = e.changedTouches[0];
    const endX = touch.clientX;
    const endY = touch.clientY;
    const endTime = Date.now();

    const deltaX = endX - this.touchStartX;
    const deltaY = endY - this.touchStartY;
    const duration = endTime - this.touchStartTime;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    // Check for tap (short duration, small distance)
    if (duration < 200 && distance < 10) {
      if (this.onTap) {
        this.onTap(e);
      }
      return;
    }

    // Check for swipe
    if (distance >= this.options.minSwipeDistance! && duration <= this.options.maxSwipeTime!) {
      let direction: 'left' | 'right' | 'up' | 'down';

      if (Math.abs(deltaX) > Math.abs(deltaY)) {
        // Horizontal swipe
        direction = deltaX > 0 ? 'right' : 'left';
      } else {
        // Vertical swipe
        direction = deltaY > 0 ? 'down' : 'up';
      }

      const swipeEvent: SwipeEvent = {
        direction,
        distance,
        duration,
        startX: this.touchStartX,
        startY: this.touchStartY,
        endX,
        endY
      };

      if (this.onSwipe) {
        this.onSwipe(swipeEvent);
      }
    }
  }

  /**
   * Set swipe event handler
   */
  setOnSwipe(handler: (event: SwipeEvent) => void): void {
    this.onSwipe = handler;
  }

  /**
   * Set tap event handler
   */
  setOnTap(handler: (event: TouchEvent) => void): void {
    this.onTap = handler;
  }

  /**
   * Set long press event handler
   */
  setOnLongPress(handler: (event: TouchEvent) => void): void {
    this.onLongPress = handler;
  }

  /**
   * Destroy the gesture handler
   */
  destroy(): void {
    this.element.removeEventListener('touchstart', this.handleTouchStart.bind(this));
    this.element.removeEventListener('touchend', this.handleTouchEnd.bind(this));
    this.element.removeEventListener('touchmove', this.handleTouchMove.bind(this));

    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
    }
  }
}

/**
 * Mobile navigation utilities
 */
export const MobileNavUtils = {
  /**
   * Toggle mobile navigation
   */
  toggleMobileNav(open?: boolean): void {
    const overlay = document.getElementById('mobileNavOverlay');
    const nav = document.getElementById('mobileNav');
    
    if (!overlay || !nav) return;

    const isOpen = !nav.classList.contains('-translate-x-full');
    const shouldOpen = open !== undefined ? open : !isOpen;

    if (shouldOpen) {
      overlay.classList.remove('hidden');
      nav.classList.remove('-translate-x-full');
      nav.classList.add('translate-x-0');
      document.body.style.overflow = 'hidden'; // Prevent background scrolling
    } else {
      overlay.classList.add('hidden');
      nav.classList.remove('translate-x-0');
      nav.classList.add('-translate-x-full');
      document.body.style.overflow = ''; // Restore scrolling
    }
  },

  /**
   * Close mobile navigation
   */
  closeMobileNav(): void {
    this.toggleMobileNav(false);
  },

  /**
   * Open mobile navigation
   */
  openMobileNav(): void {
    this.toggleMobileNav(true);
  },

  /**
   * Check if mobile navigation is open
   */
  isMobileNavOpen(): boolean {
    const nav = document.getElementById('mobileNav');
    return nav ? !nav.classList.contains('-translate-x-full') : false;
  }
};

/**
 * Viewport utilities for responsive behavior
 */
export const ViewportUtils = {
  /**
   * Add viewport change listener
   */
  onViewportChange(callback: (size: { width: number; height: number }) => void): () => void {
    const handleResize = () => {
      callback(DeviceUtils.getViewportSize());
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleResize);

    // Return cleanup function
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleResize);
    };
  },

  /**
   * Get safe area insets for devices with notches
   */
  getSafeAreaInsets(): { top: number; right: number; bottom: number; left: number } {
    const style = getComputedStyle(document.documentElement);
    return {
      top: parseInt(style.getPropertyValue('env(safe-area-inset-top)') || '0'),
      right: parseInt(style.getPropertyValue('env(safe-area-inset-right)') || '0'),
      bottom: parseInt(style.getPropertyValue('env(safe-area-inset-bottom)') || '0'),
      left: parseInt(style.getPropertyValue('env(safe-area-inset-left)') || '0')
    };
  },

  /**
   * Apply safe area padding to an element
   */
  applySafeAreaPadding(element: HTMLElement): void {
    const insets = this.getSafeAreaInsets();
    element.style.paddingTop = `max(${element.style.paddingTop || '0'}, ${insets.top}px)`;
    element.style.paddingRight = `max(${element.style.paddingRight || '0'}, ${insets.right}px)`;
    element.style.paddingBottom = `max(${element.style.paddingBottom || '0'}, ${insets.bottom}px)`;
    element.style.paddingLeft = `max(${element.style.paddingLeft || '0'}, ${insets.left}px)`;
  }
};

/**
 * Performance utilities for mobile devices
 */
export const MobilePerformanceUtils = {
  /**
   * Debounce function for performance optimization
   */
  debounce<T extends (...args: any[]) => any>(func: T, wait: number): T {
    let timeout: number;
    return ((...args: any[]) => {
      clearTimeout(timeout);
      timeout = window.setTimeout(() => func.apply(this, args), wait);
    }) as T;
  },

  /**
   * Throttle function for performance optimization
   */
  throttle<T extends (...args: any[]) => any>(func: T, limit: number): T {
    let inThrottle: boolean;
    return ((...args: any[]) => {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    }) as T;
  },

  /**
   * Request animation frame with fallback
   */
  requestAnimationFrame(callback: FrameRequestCallback): number {
    return window.requestAnimationFrame || 
           window.webkitRequestAnimationFrame || 
           ((callback: FrameRequestCallback) => window.setTimeout(callback, 1000 / 60));
  },

  /**
   * Optimize images for mobile devices
   */
  optimizeImagesForMobile(): void {
    const images = document.querySelectorAll('img');
    images.forEach(img => {
      // Add loading="lazy" for better performance
      if (!img.hasAttribute('loading')) {
        img.setAttribute('loading', 'lazy');
      }
      
      // Ensure responsive sizing
      if (!img.style.maxWidth) {
        img.style.maxWidth = '100%';
        img.style.height = 'auto';
      }
    });
  }
};

/**
 * Initialize mobile-specific features
 */
export const initializeMobileFeatures = (): void => {
  // Optimize images
  MobilePerformanceUtils.optimizeImagesForMobile();

  // Add touch-friendly classes to interactive elements
  const interactiveElements = document.querySelectorAll('button, a, input, select, textarea');
  interactiveElements.forEach(element => {
    const htmlElement = element as HTMLElement;
    if (!htmlElement.style.minHeight) {
      htmlElement.style.minHeight = '44px';
    }
  });

  // Add viewport meta tag if not present
  if (!document.querySelector('meta[name="viewport"]')) {
    const viewport = document.createElement('meta');
    viewport.name = 'viewport';
    viewport.content = 'width=device-width, initial-scale=1.0, viewport-fit=cover, user-scalable=no';
    document.head.appendChild(viewport);
  }

  console.log('📱 Mobile features initialized');
};
