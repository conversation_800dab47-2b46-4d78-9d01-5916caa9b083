const passport = require('passport');
const GoogleStrategy = require('passport-google-oauth20').Strategy;
const { upsertUser } = require('../services/promptDbService');

// User serialization (for session)
passport.serializeUser((user, done) => {
  console.log('[Auth Debug] Serializing user:', user.id, 'dbId:', user.dbId);

  if (!user || !user.id) {
    console.error('[Auth Debug] Cannot serialize user - missing user or user.id');
    return done(new Error('Invalid user object for serialization'));
  }

  // Store both the provider ID and the database ID
  const serializedUser = {
    id: user.id,
    dbId: user.dbId,
    provider: user.provider,
    displayName: user.displayName,
    email: user.email
  };

  console.log('[Auth Debug] Serialized user data:', serializedUser);
  done(null, serializedUser);
});

passport.deserializeUser(async (serializedUser, done) => {
  try {
    console.log('[Auth Debug] Deserializing user:', JSON.stringify(serializedUser, null, 2));

    if (!serializedUser) {
      console.error('[Auth Debug] No serialized user data');
      return done(null, false);
    }

    // If we have a database ID, we can fetch the full user from the database
    if (serializedUser.dbId) {
      // In a production app, you would fetch the full user from the database here
      // For now, we'll just use what we have in the session
      console.log('[Auth Debug] Successfully deserialized user with dbId:', serializedUser.dbId);
      done(null, serializedUser);
    } else {
      // If we don't have a database ID, just use what we have
      console.log('[Auth Debug] Successfully deserialized user without dbId');
      done(null, serializedUser);
    }
  } catch (error) {
    console.error('[Auth Debug] Error deserializing user:', error);
    console.error('[Auth Debug] Error stack:', error.stack);
    done(error);
  }
});

// Configure Google OAuth strategy
passport.use(new GoogleStrategy(
  {
    clientID: process.env.GOOGLE_CLIENT_ID,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    callbackURL: process.env.GOOGLE_CALLBACK_URL || '/api/auth/google/callback',
  },
  async (_accessToken, _refreshToken, profile, done) => {
    try {
      console.log('[Auth Debug] Google auth callback received profile:', profile.id);

      // Extract user data from profile
      const email = profile.emails && profile.emails[0] ? profile.emails[0].value : null;
      const photo_url = profile.photos && profile.photos[0] ? profile.photos[0].value : null;

      if (!email) {
        return done(new Error('Email is required for authentication'));
      }

      // Save user to database
      const userData = {
        google_id: profile.id,
        email: email,
        display_name: profile.displayName,
        photo_url: photo_url,
        auth_provider: 'google',
        email_verified: true // Google already verified the email
      };

      console.log('[Auth Debug] Upserting user with data:', userData);

      // Upsert user in database
      const userId = await upsertUser(userData);
      console.log('[Auth Debug] User upserted with database ID:', userId);

      if (!userId) {
        console.error('[Auth Debug] Failed to get user ID from database');
        return done(new Error('Failed to create/find user in database'));
      }

      // Return user object for session
      const userObject = {
        id: profile.id,
        dbId: userId, // Include the database ID
        displayName: profile.displayName,
        email: email,
        photo: photo_url,
        provider: profile.provider,
      };

      console.log('[Auth Debug] Returning user object for session:', userObject);
      return done(null, userObject);
    } catch (error) {
      console.error('[Auth Debug] Error in Google auth callback:', error);
      console.error('[Auth Debug] Error stack:', error.stack);
      console.error('[Auth Debug] Profile data:', profile);
      return done(error);
    }
  }
));

function ensureAuthenticated(req, res, next) {
  // Add CORS headers for all authenticated routes
  res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Cookie, X-Requested-With, Accept, Cache-Control');
  res.header('Access-Control-Expose-Headers', 'Set-Cookie, X-Auth-Status');

  console.log('[Auth Debug] ensureAuthenticated called for path:', req.path);
  console.log('[Auth Debug] Origin:', req.headers.origin);
  console.log('[Auth Debug] Session ID:', req.session?.id);
  console.log('[Auth Debug] Session passport data:', req.session?.passport);
  //console.log('[Auth Debug] Cookies received:', req.headers.cookie);
  console.log('[Auth Debug] isAuthenticated:', req.isAuthenticated && req.isAuthenticated());

  if (req.isAuthenticated && req.isAuthenticated()) {
    return next();
  }

  // Fallback: Check for userData cookie if session auth fails
  const userDataCookie = req.cookies?.userData;
  const isLoggedInCookie = req.cookies?.isLoggedIn;

  console.log('[Auth Debug] Session auth failed, checking cookies');
  console.log('[Auth Debug] isLoggedIn cookie:', isLoggedInCookie);
  console.log('[Auth Debug] userData cookie exists:', !!userDataCookie);

  if (isLoggedInCookie === 'true' && userDataCookie) {
    try {
      const userData = JSON.parse(userDataCookie);
      console.log('[Auth Debug] Using cookie-based auth for user:', userData.id);

      // Attach user data to request for downstream middleware
      req.user = userData;
      return next();
    } catch (error) {
      console.error('[Auth Debug] Error parsing userData cookie:', error);
    }
  }

  // Set CORS headers even for error responses
  console.log('[Auth Debug] All authentication methods failed');
  res.status(401).json({ error: 'Not authenticated' });
}

module.exports = {
  passport,
  ensureAuthenticated,
};
