import React, { useState, useRef, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { FiSend, FiLoader, FiCode, FiEye } from 'react-icons/fi';
import { processStreamingDiffResponse, type StreamingDiffResponse } from '../services/diffUtils';
import ShareButton from '../components/ShareButton';

/**
 * EditorPageV3 - Clean implementation based on Readdy.ai approach
 * Features:
 * - Conversational interface like Readdy.ai
 * - Real-time streaming
 * - Targeted editing capabilities
 * - Clean, modern UI
 */

type ViewMode = 'preview' | 'code';

interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  type?: 'plan' | 'code' | 'message';
}

export function EditorPageV3() {
  const location = useLocation();
  const navigate = useNavigate();
  const { prompt, plan, initialGeneration } = location.state || {};

  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      role: 'assistant',
      content: 'Hi! I\'m your AI design assistant. Describe what you\'d like to create and I\'ll build it for you.',
      timestamp: new Date()
    }
  ]);

  const [input, setInput] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [htmlContent, setHtmlContent] = useState('');
  const [viewMode, setViewMode] = useState<ViewMode>('preview');
  const [streamingContent, setStreamingContent] = useState('');
  const [selectionMode, setSelectionMode] = useState(false);
  const [selectedElement, setSelectedElement] = useState<any>(null);
  const [showImplementModal, setShowImplementModal] = useState(false);
  const [isLinking, setIsLinking] = useState(false);
  const [linkingQueue, setLinkingQueue] = useState<string[]>([]);

  // Multi-page management
  const [pages, setPages] = useState<Array<{
    id: string;
    name: string;
    content: string;
    isActive: boolean;
    lastUpdated?: Date;
  }>>([
    { id: 'main', name: 'Main Page', content: '', isActive: true, lastUpdated: new Date() }
  ]);
  const [currentPageId, setCurrentPageId] = useState('main');

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // Extract clean HTML from response
  const extractHtmlFromResponse = (response: string): string => {
    if (!response) return '';

    // Look for HTML content between ```html and ``` markers
    const htmlMatch = response.match(/```html\s*([\s\S]*?)\s*```/);
    if (htmlMatch) {
      return htmlMatch[1].trim();
    }

    // Look for HTML starting with DOCTYPE or html tag
    const doctypeMatch = response.match(/(<!DOCTYPE html[\s\S]*)/i);
    if (doctypeMatch) {
      return doctypeMatch[1].trim();
    }

    const htmlTagMatch = response.match(/(<html[\s\S]*)/i);
    if (htmlTagMatch) {
      console.log('Found HTML with html tag');
      return htmlTagMatch[1].trim();
    }

    // If response contains HTML tags, assume it's HTML
    if (response.includes('<') && response.includes('>')) {
      // Remove any text before the first HTML tag
      const firstTagIndex = response.indexOf('<');
      const extracted = response.substring(firstTagIndex).trim();
      console.log('Found HTML content, extracted:', extracted.substring(0, 100) + '...');
      return extracted;
    }

    console.log('No HTML found in response');
    return response;
  };

  // Format HTML code for display with proper indentation
  const formatHtmlCode = (html: string): string => {
    if (!html) return '';

    try {
      // If HTML is already well-formatted, preserve it
      if (html.includes('\n') && html.includes('  ')) {
        return html;
      }

      // Basic HTML formatting with proper indentation
      let formatted = html
        // Normalize whitespace but preserve content
        .replace(/>\s*</g, '>\n<')
        // Add newlines after opening tags (block elements)
        .replace(/(<(?!\/)(div|section|article|header|footer|nav|main|aside|h[1-6]|p|ul|ol|li|form|fieldset|table|thead|tbody|tr|td|th|html|head|body|script|style)[^>]*>)/gi, '$1\n')
        // Add newlines before closing tags
        .replace(/(<\/(div|section|article|header|footer|nav|main|aside|h[1-6]|p|ul|ol|li|form|fieldset|table|thead|tbody|tr|html|head|body|script|style)>)/gi, '\n$1')
        // Add newlines after self-closing tags
        .replace(/(<[^>]*\/>)/g, '$1\n')
        // Handle CSS and JS blocks specially
        .replace(/(<style[^>]*>)([\s\S]*?)(<\/style>)/gi, (_, openTag, content, closeTag) => {
          const formattedCSS = content
            .replace(/\s*{\s*/g, ' {\n  ')
            .replace(/;\s*/g, ';\n  ')
            .replace(/\s*}\s*/g, '\n}\n');
          return `${openTag}\n${formattedCSS}${closeTag}`;
        })
        .replace(/(<script[^>]*>)([\s\S]*?)(<\/script>)/gi, (_, openTag, content, closeTag) => {
          return `${openTag}\n${content}\n${closeTag}`;
        });

      // Split into lines and add proper indentation
      const lines = formatted.split('\n').map(line => line.trim()).filter(line => line.length > 0);

      let indentLevel = 0;
      const indentedLines = lines.map(line => {
        // Handle CSS content
        if (line.includes('{') && !line.startsWith('<')) {
          return '  '.repeat(indentLevel + 1) + line;
        }
        if (line === '}' && !line.startsWith('<')) {
          return '  '.repeat(indentLevel + 1) + line;
        }
        if (line.includes(':') && line.includes(';') && !line.startsWith('<')) {
          return '  '.repeat(indentLevel + 2) + line;
        }

        // Decrease indent for closing tags
        if (line.startsWith('</')) {
          indentLevel = Math.max(0, indentLevel - 1);
        }

        const indentedLine = '  '.repeat(indentLevel) + line;

        // Increase indent for opening tags (but not self-closing or closing tags)
        if (line.startsWith('<') && !line.startsWith('</') && !line.endsWith('/>') && !line.includes('<!DOCTYPE')) {
          // Don't increase indent for inline elements
          const inlineElements = ['a', 'span', 'strong', 'em', 'b', 'i', 'small', 'code', 'img', 'input', 'button'];
          const tagName = line.match(/<(\w+)/)?.[1]?.toLowerCase();
          if (!inlineElements.includes(tagName || '')) {
            indentLevel++;
          }
        }

        return indentedLine;
      });

      return indentedLines.join('\n');
    } catch (error) {
      console.error('Error formatting HTML:', error);
      return html;
    }
  };

  // Helper function to format plan content like Readdy.ai
  const formatPlanContent = (content: string): string => {
    if (!content) return '';

    return content
      // Convert numbered sections to headers
      .replace(/^(\d+\.\s+)(.+)$/gm, '<h3 class="text-lg font-semibold text-gray-900 mt-4 mb-2">$1$2</h3>')
      // Convert bullet points to styled lists
      .replace(/^[\s]*[-•]\s+(.+)$/gm, '<li class="ml-4 mb-1 text-gray-700">$1</li>')
      // Wrap consecutive list items in ul tags
      .replace(/(<li[^>]*>.*<\/li>\s*)+/gs, '<ul class="space-y-1 mb-3">$&</ul>')
      // Convert bold text
      .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-gray-900">$1</strong>')
      // Convert paragraphs
      .replace(/^(?!<[hl]|<ul)(.+)$/gm, '<p class="mb-3 text-gray-700 leading-relaxed">$1</p>')
      // Clean up extra spacing
      .replace(/\n\s*\n/g, '\n')
      .trim();
  };

  // Auto-scroll to bottom of messages (only when new messages are added, not during streaming)
  useEffect(() => {
    if (!isGenerating) {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, isGenerating]);

  // Handle initial generation from plan page
  useEffect(() => {
    if (initialGeneration && prompt && !htmlContent) {
      // Add user message
      const userMessage: ChatMessage = {
        role: 'user',
        content: prompt,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, userMessage]);

      // Add plan message if we have a plan
      if (plan) {
        let planContent = '';
        if (typeof plan === 'string') {
          planContent = plan;
        } else if (plan && typeof plan === 'object') {
          // Format the plan object nicely
          planContent = `📋 **Project Overview**\n${plan.overview || 'No overview provided'}\n\n`;

          if (plan.sections && Array.isArray(plan.sections)) {
            planContent += `🏗️ **Implementation Sections**\n`;
            plan.sections.forEach((section, index) => {
              planContent += `\n${index + 1}. **${section.title}**\n`;
              planContent += `   ${section.description}\n`;
              if (section.details && Array.isArray(section.details)) {
                section.details.forEach(detail => {
                  planContent += `   • ${detail}\n`;
                });
              }
            });
          }

          if (plan.features && Array.isArray(plan.features)) {
            planContent += `\n✨ **Key Features**\n`;
            plan.features.forEach(feature => {
              planContent += `• ${feature}\n`;
            });
          }

          if (plan.accessibility && Array.isArray(plan.accessibility)) {
            planContent += `\n♿ **Accessibility Features**\n`;
            plan.accessibility.forEach(item => {
              planContent += `• ${item}\n`;
            });
          }
        }

        const planMessage: ChatMessage = {
          role: 'assistant',
          content: planContent,
          timestamp: new Date(),
          type: 'plan'
        };
        setMessages(prev => [...prev, planMessage]);
      }

      // Start generation
      generateFromPrompt(prompt);
    }
  }, [initialGeneration, prompt, htmlContent, plan]);

  // Generate from prompt (for initial generation)
  const generateFromPrompt = async (promptText: string) => {
    setIsGenerating(true);
    setStreamingContent('');

    try {
      const response = await fetch('/api/llm/v3/generate-html', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ prompt: promptText })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error:', response.status, errorText);
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body');
      }

      let accumulatedContent = '';
      let isCollectingHTML = false;

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = new TextDecoder().decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('event:')) {
            const event = line.substring(6);
            if (event === 'start') {
              isCollectingHTML = true;
            } else if (event === 'end') {
              isCollectingHTML = false;
              // Extract clean HTML and set final content
              const cleanHtml = extractHtmlFromResponse(accumulatedContent);
              setHtmlContent(cleanHtml);
              setStreamingContent('');
            }
          } else if (line.startsWith('data:') && isCollectingHTML) {
            const data = line.substring(5);
            accumulatedContent += data;
            setStreamingContent(accumulatedContent);
          }
        }
      }

      // Add assistant response
      const assistantMessage: ChatMessage = {
        role: 'assistant',
        content: 'I\'ve created your design! What would you like to modify?',
        timestamp: new Date()
      };

      setMessages(prev => [...prev, assistantMessage]);

    } catch (error) {
      console.error('Error:', error);
      const errorMessage: ChatMessage = {
        role: 'assistant',
        content: 'Sorry, I encountered an error. Please try again.',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsGenerating(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isGenerating) return;

    const userMessage: ChatMessage = {
      role: 'user',
      content: input.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsGenerating(true);
    setStreamingContent('');

    try {
      // Determine if this is an edit or new generation
      const isEdit = htmlContent.length > 0 || stableIframeContent.length > 0;
      const endpoint = isEdit ? '/api/llm/v3/edit' : '/api/llm/v3/generate-html';

      // Use the most recent HTML content for editing
      const currentHtmlContent = htmlContent || stableIframeContent;

      const requestBody = isEdit
        ? { htmlContent: currentHtmlContent, prompt: userMessage.content }
        : { prompt: userMessage.content };

      console.log('Edit request:', { isEdit, endpoint, hasContent: !!currentHtmlContent });

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error:', response.status, errorText);
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body');
      }

      let accumulatedContent = '';
      let isCollectingHTML = false;
      let originalHtml = htmlContent; // Store original for diff processing

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = new TextDecoder().decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('event:')) {
            const event = line.substring(6);
            if (event === 'start') {
              isCollectingHTML = true;
            } else if (event === 'end') {
              isCollectingHTML = false;
              // Extract clean HTML and set final content
              const cleanHtml = extractHtmlFromResponse(accumulatedContent);
              setHtmlContent(cleanHtml);
              setStreamingContent('');
            } else if (event === 'diff') {
              // Handle diff event - look for the next data line
              isCollectingHTML = false; // Stop collecting regular HTML
            }
          } else if (line.startsWith('data:')) {
            const data = line.substring(5);

            // Check if this is diff data (JSON format)
            if (data.startsWith('{') && data.includes('shouldUseDiff')) {
              try {
                const diffData = JSON.parse(data);
                const diffResponse: StreamingDiffResponse = {
                  type: 'diff',
                  data: diffData,
                  timestamp: new Date().toISOString()
                };

                // Process diff and update HTML
                const updatedHtml = processStreamingDiffResponse(originalHtml, diffResponse);

                // Set the HTML content directly without extraction since it's already clean
                setHtmlContent(updatedHtml);
                setStreamingContent('');

                // Also update the stable iframe content for immediate display
                setStableIframeContent(updatedHtml);

                accumulatedContent = updatedHtml; // Update accumulated content
                isCollectingHTML = false; // Stop collecting more data
              } catch (error) {
                console.error('❌ Error processing diff data:', error);
                // Fall back to treating as regular HTML data
                if (isCollectingHTML) {
                  accumulatedContent += data;
                  setStreamingContent(accumulatedContent);
                }
              }
            } else if (isCollectingHTML) {
              // Regular HTML streaming data
              accumulatedContent += data;
              setStreamingContent(accumulatedContent);
            }
          }
        }
      }

      // Add assistant response
      const assistantMessage: ChatMessage = {
        role: 'assistant',
        content: isEdit ? 'I\'ve updated your design. What would you like to change next?' : 'I\'ve created your design! What would you like to modify?',
        timestamp: new Date()
      };

      setMessages(prev => [...prev, assistantMessage]);

    } catch (error) {
      console.error('Error:', error);
      const errorMessage: ChatMessage = {
        role: 'assistant',
        content: 'Sorry, I encountered an error. Please try again.',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsGenerating(false);
    }
  };

  // Get current content for display
  const rawContent = streamingContent || htmlContent;
  const cleanHtmlContent = extractHtmlFromResponse(rawContent);
  const formattedCodeContent = formatHtmlCode(cleanHtmlContent);

  // Create stable iframe content - only update when we have complete HTML
  const [stableIframeContent, setStableIframeContent] = useState('');

  useEffect(() => {
    // Only update iframe when generation is complete and we have valid HTML
    if (cleanHtmlContent && !isGenerating) {
      // Check if we have a complete HTML document or at least some HTML content
      if (cleanHtmlContent.includes('</html>') ||
          (cleanHtmlContent.includes('<') && cleanHtmlContent.includes('>') && cleanHtmlContent.length > 50)) {
        console.log('Updating stable iframe content:', cleanHtmlContent.substring(0, 100) + '...');

        // Ensure we have a complete HTML document
        let completeHtml = cleanHtmlContent;
        if (!completeHtml.includes('<!DOCTYPE html')) {
          completeHtml = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generated Content</title>
</head>
<body>
    ${completeHtml}
</body>
</html>`;
        }

        // Add interaction detection script
        console.log('🔥 Adding interaction detection to generated content...');
        const interactiveHtml = addInteractionDetection(completeHtml);
        console.log('🔥 Interactive HTML length:', interactiveHtml.length);
        console.log('🔥 Setting iframe content with interaction detection');
        setStableIframeContent(interactiveHtml);

        // Update current page content
        setPages(prev => prev.map(page =>
          page.id === currentPageId
            ? { ...page, content: interactiveHtml }
            : page
        ));
      }
    }
  }, [cleanHtmlContent, isGenerating, currentPageId]);

  // Add interaction detection to HTML (completely rewritten for reliability)
  const addInteractionDetection = (html: string): string => {
    // Remove any existing scripts and indicators first
    let cleanHtml = html
      .replace(/<script[\s\S]*?<\/script>/gi, '')
      .replace(/⚡/g, '')
      .replace(/class="[^"]*unimplemented-indicator[^"]*"/g, '');

    const interactionScript = `
    <script>
      console.log('🔥 Interaction detection script starting...');

      function initializeInteractionDetection() {
        console.log('🔥 Initializing interaction detection...');

        // Find ALL interactive elements
        const selectors = [
          'a', 'button', '.btn', '.button',
          '[onclick]', '[data-action]',
          'nav a', '.nav-link', '.navbar a',
          'input[type="submit"]', 'input[type="button"]',
          '[role="button"]', '.clickable'
        ];

        const interactiveElements = document.querySelectorAll(selectors.join(', '));
        console.log('🔥 Found', interactiveElements.length, 'interactive elements');

        interactiveElements.forEach((element, index) => {
          const text = element.textContent?.trim() || '';
          console.log('🔥 Processing element', index + 1, ':', element.tagName, '"' + text + '"');

          // Skip if already processed
          if (element.dataset.interactionProcessed) {
            console.log('🔥 Element already processed, skipping');
            return;
          }
          element.dataset.interactionProcessed = 'true';

          // Check if element has working functionality
          const hasWorkingFunction = element.onclick ||
                                   (element.tagName === 'BUTTON' && element.type === 'submit') ||
                                   element.getAttribute('data-implemented') === 'true' ||
                                   (element.tagName === 'A' && element.href && !element.href.includes('#'));

          // Style the element
          element.style.cursor = 'pointer';
          element.style.position = 'relative';

          // Remove href to prevent navigation
          if (element.tagName === 'A' && element.href) {
            element.removeAttribute('href');
          }

          // Add indicator for unimplemented features
          if (!hasWorkingFunction) {
            console.log('🔥 Adding indicator to:', text);

            // Create indicator
            const indicator = document.createElement('div');
            indicator.innerHTML = '⚡';
            indicator.className = 'unimplemented-indicator';
            indicator.style.cssText = \`
              position: absolute !important;
              top: -8px !important;
              right: -8px !important;
              background: #ef4444 !important;
              color: white !important;
              border-radius: 50% !important;
              width: 20px !important;
              height: 20px !important;
              display: flex !important;
              align-items: center !important;
              justify-content: center !important;
              font-size: 12px !important;
              z-index: 99999 !important;
              opacity: 0.95 !important;
              font-weight: bold !important;
              box-shadow: 0 2px 6px rgba(0,0,0,0.3) !important;
              pointer-events: none !important;
            \`;

            element.appendChild(indicator);

            // Add hover effects
            element.addEventListener('mouseenter', function() {
              indicator.style.transform = 'scale(1.2)';
              indicator.style.opacity = '1';
              element.style.transform = 'scale(1.02)';
            });

            element.addEventListener('mouseleave', function() {
              indicator.style.transform = 'scale(1)';
              indicator.style.opacity = '0.95';
              element.style.transform = 'scale(1)';
            });
          }

          // Add click handler
          element.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();

            console.log('🔥 Element clicked:', text);

            const elementText = text.toLowerCase();

            // Check if this is a close button
            const isCloseButton = elementText === '×' ||
                                elementText === 'x' ||
                                elementText.includes('close') ||
                                element.className.includes('close') ||
                                element.getAttribute('aria-label')?.includes('close');

            // Check if this looks like navigation
            const isNavigation = elementText && (
              elementText.includes('about') ||
              elementText.includes('contact') ||
              elementText.includes('menu') ||
              elementText.includes('home') ||
              elementText.includes('services') ||
              elementText.includes('portfolio') ||
              elementText.includes('blog') ||
              elementText.includes('sign up') ||
              elementText.includes('signup') ||
              elementText.includes('register') ||
              elementText.includes('login') ||
              elementText.includes('sign in') ||
              elementText.includes('signin') ||
              elementText.includes('dashboard') ||
              elementText.includes('profile') ||
              elementText.includes('account') ||
              elementText.includes('shop') ||
              elementText.includes('cart') ||
              elementText.includes('checkout') ||
              elementText.includes('products') ||
              elementText.includes('pricing') ||
              elementText.includes('features') ||
              elementText.includes('gallery') ||
              elementText.includes('testimonials') ||
              elementText.includes('faq') ||
              elementText.includes('help') ||
              elementText.includes('support') ||
              element.tagName === 'A' ||
              element.closest('nav') ||
              element.className.includes('nav')
            );

            console.log('🔥 Sending message to parent:', {
              text: text,
              isNavigation: isNavigation,
              isCloseButton: isCloseButton
            });

            // Send message to parent window
            window.parent.postMessage({
              type: 'ELEMENT_CLICKED',
              element: {
                tagName: element.tagName,
                className: element.className,
                id: element.id,
                textContent: text,
                outerHTML: element.outerHTML,
                isNavigation: isNavigation,
                isCloseButton: isCloseButton
              }
            }, '*');
          }, true);
        });

        console.log('🔥 Interaction detection initialization complete!');
      }

      // Initialize when DOM is ready
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeInteractionDetection);
      } else {
        initializeInteractionDetection();
      }

      // Also initialize after a short delay to catch dynamically added elements
      setTimeout(initializeInteractionDetection, 500);
    </script>`;

    // Insert script before closing body tag
    const result = cleanHtml.replace('</body>', interactionScript + '</body>');
    console.log('🔥 Added interaction detection script to HTML');
    return result;
  };

  // Auto-test navigation linking on component mount (for debugging)
  useEffect(() => {
    // Auto-run test after 3 seconds if no pages exist
    const timer = setTimeout(() => {
      if (pages.length === 0) {
        console.log('🧪 Auto-creating test pages for debugging...');
        const testPages = [
          {
            id: 'main-page',
            name: 'Main Page',
            content: `<!DOCTYPE html><html><head><title>Main</title></head><body><h1>Main Page</h1><p>Content here</p></body></html>`,
            isActive: false
          },
          {
            id: 'about',
            name: 'About',
            content: `<!DOCTYPE html><html><head><title>About</title></head><body><h1>About</h1><p>About content</p></body></html>`,
            isActive: false
          }
        ];
        setPages(testPages);
        setCurrentPageId('main-page');
        setStableIframeContent(testPages[0].content);

        // Auto-test linking after pages are created
        setTimeout(() => {
          console.log('🧪 Auto-testing navigation linking...');
          updateMainPageNavigation();
        }, 1000);
      }
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  // Listen for iframe interactions
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      console.log('🔥 Message received:', event.data);

      if (event.data.type === 'ELEMENT_CLICKED') {
        const element = event.data.element;
        console.log('🔥 Element clicked in iframe:', element);

        // If it's a close button, handle modal/popup closing
        if (element.isCloseButton) {
          console.log('🔥 Close button detected, implementing close functionality');
          handleCloseButtonClick(element);
        }
        // If it's navigation, handle page switching/creation
        else if (element.isNavigation) {
          console.log('🔥 Navigation detected, handling navigation click');
          handleNavigationClick(element);
        } else {
          console.log('🔥 Non-navigation element, showing modal');
          setSelectedElement(element);
          setShowImplementModal(true);
        }
      }
    };

    console.log('🔥 Setting up message listener');
    window.addEventListener('message', handleMessage);
    return () => {
      console.log('🔥 Removing message listener');
      window.removeEventListener('message', handleMessage);
    };
  }, [pages]); // Add pages dependency to ensure latest pages are available

  // Handle close button clicks - implement close functionality
  const handleCloseButtonClick = async (element: any) => {
    console.log('🔴 Implementing close functionality for:', element);

    const prompt = `The user clicked a close button (${element.textContent}). Please implement proper close functionality:

1. Add JavaScript to close the modal/popup when the close button is clicked
2. Add click outside to close functionality
3. Add escape key to close functionality
4. Ensure the modal/popup disappears completely
5. Remove any backdrop/overlay
6. Make sure the page remains functional after closing

Current element: ${element.outerHTML}

Implement a fully functional close mechanism with proper event handling.`;

    // Add the implementation request as a message and trigger generation
    const implementMessage: ChatMessage = {
      role: 'user',
      content: prompt,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, implementMessage]);
    setIsGenerating(true);
    setStreamingContent('');

    try {
      // Use edit endpoint since we're modifying existing content
      const endpoint = '/api/llm/v3/edit';
      const currentHtmlContent = htmlContent || stableIframeContent;

      const requestBody = {
        htmlContent: currentHtmlContent,
        prompt: prompt
      };

      console.log('Close button implementation request:', { prompt });

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error:', response.status, errorText);
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body');
      }

      const decoder = new TextDecoder();
      let accumulatedContent = '';
      let isCollectingHTML = false;

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('event:')) {
            const event = line.substring(6);
            if (event === 'start') {
              isCollectingHTML = true;
            } else if (event === 'end') {
              isCollectingHTML = false;
              const cleanHtml = extractHtmlFromResponse(accumulatedContent);
              console.log('Close functionality implementation complete:', cleanHtml.substring(0, 100) + '...');

              // Ensure complete HTML document
              let completeHtml = cleanHtml;
              if (!completeHtml.includes('<!DOCTYPE html')) {
                completeHtml = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generated Content</title>
</head>
<body>
    ${completeHtml}
</body>
</html>`;
              }

              // Add interaction detection but remove indicators from implemented elements
              const interactiveHtml = addInteractionDetection(completeHtml);
              setHtmlContent(cleanHtml);
              setStableIframeContent(interactiveHtml);

              // Update current page content
              setPages(prev => prev.map(page =>
                page.id === currentPageId
                  ? { ...page, content: interactiveHtml }
                  : page
              ));

              setStreamingContent('');
            }
          } else if (line.startsWith('data:') && isCollectingHTML) {
            const data = line.substring(5);
            accumulatedContent += data;
            setStreamingContent(accumulatedContent);
          }
        }
      }

      // Add AI response message
      const aiMessage: ChatMessage = {
        role: 'assistant',
        content: `✅ Implemented close functionality for "${element.textContent}"`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, aiMessage]);

    } catch (error) {
      console.error('Error implementing close functionality:', error);
      const errorMessage: ChatMessage = {
        role: 'assistant',
        content: `❌ Error implementing close functionality: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsGenerating(false);
    }
  };

  // Handle feature implementation based on type
  const handleImplementFeature = async (implementationType: 'modal' | 'page' | 'inline') => {
    if (!selectedElement) return;

    setShowImplementModal(false);

    const elementText = selectedElement.textContent;
    const elementType = selectedElement.tagName.toLowerCase();

    let prompt = '';

    switch (implementationType) {
      case 'modal':
        prompt = `Add a modal popup for the "${elementText}" ${elementType}. When clicked, it should open a modal with relevant content and functionality. Include proper modal styling, backdrop, close button, and any necessary form fields or content related to "${elementText}".`;
        break;
      case 'page':
        prompt = `Convert the "${elementText}" ${elementType} to navigate to a new page. Create a complete new page layout with navigation back to the main page. The new page should contain relevant content and functionality for "${elementText}".`;
        break;
      case 'inline':
        prompt = `Implement the "${elementText}" ${elementType} functionality directly on the current page. Add any necessary forms, content sections, or interactive elements that would be expected when clicking "${elementText}".`;
        break;
    }

    // Add the implementation request as a message and trigger generation
    const implementMessage: ChatMessage = {
      role: 'user',
      content: prompt,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, implementMessage]);
    setInput('');
    setIsGenerating(true);
    setStreamingContent('');

    try {
      // Use edit endpoint since we're modifying existing content
      const isEdit = htmlContent.length > 0 || stableIframeContent.length > 0;
      const endpoint = '/api/llm/v3/edit';
      const currentHtmlContent = htmlContent || stableIframeContent;

      const requestBody = {
        htmlContent: currentHtmlContent,
        prompt: prompt
      };

      console.log('Implementation request:', { implementationType, prompt });

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error:', response.status, errorText);
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body');
      }

      const decoder = new TextDecoder();
      let accumulatedContent = '';
      let isCollectingHTML = false;

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('event:')) {
            const event = line.substring(6);
            if (event === 'start') {
              isCollectingHTML = true;
            } else if (event === 'end') {
              isCollectingHTML = false;
              const cleanHtml = extractHtmlFromResponse(accumulatedContent);
              console.log('Implementation complete, setting HTML content:', cleanHtml.substring(0, 100) + '...');
              setHtmlContent(cleanHtml);
              setStreamingContent('');
            }
          } else if (line.startsWith('data:') && isCollectingHTML) {
            const data = line.substring(5);
            accumulatedContent += data;
            setStreamingContent(accumulatedContent);
          }
        }
      }

      // Add AI response message
      const aiMessage: ChatMessage = {
        role: 'assistant',
        content: `✅ Implemented "${elementText}" as ${implementationType}`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, aiMessage]);

    } catch (error) {
      console.error('Error implementing feature:', error);
      const errorMessage: ChatMessage = {
        role: 'assistant',
        content: `❌ Error implementing feature: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsGenerating(false);
      setSelectedElement(null);
    }
  };

  // Handle navigation click - improved logic for page switching and creation
  const handleNavigationClick = async (element: any) => {
    console.log('🔥 handleNavigationClick called with element:', element);

    const pageName = element.textContent.trim();
    // Better ID normalization - remove special characters and normalize
    const pageId = pageName.toLowerCase()
      .replace(/[^a-z0-9\s]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens

    console.log('🔥 Processing navigation click:', { pageName, pageId, existingPages: pages.map(p => ({ id: p.id, name: p.name })) });

    // Check if page already exists (more robust check with better normalization)
    const normalizedPageName = pageName.toLowerCase().trim();
    const existingPage = pages.find(p => {
      const normalizedExistingName = p.name.toLowerCase().trim();
      const normalizedExistingId = p.id.toLowerCase().trim();

      return p.id === pageId ||
             normalizedExistingName === normalizedPageName ||
             normalizedExistingId === pageId ||
             // Check for partial matches to catch variations
             (normalizedPageName.includes('sign up') && normalizedExistingName.includes('sign up')) ||
             (normalizedPageName.includes('contact') && normalizedExistingName.includes('contact')) ||
             (normalizedPageName.includes('about') && normalizedExistingName.includes('about')) ||
             (normalizedPageName.includes('home') && (normalizedExistingName.includes('main') || normalizedExistingName.includes('home'))) ||
             (normalizedPageName.includes('main') && (normalizedExistingName.includes('main') || normalizedExistingName.includes('home')))
    });

    if (existingPage) {
      console.log('🔥 Page already exists, switching to:', existingPage);
      // Switch to existing page
      setCurrentPageId(existingPage.id);
      if (existingPage.content) {
        console.log('🔥 Loading existing page content, length:', existingPage.content.length);
        setStableIframeContent(existingPage.content);
        setHtmlContent(existingPage.content);
      } else {
        console.log('🔥 Existing page has no content, clearing iframe');
        setStableIframeContent('');
        setHtmlContent('');
      }

      // Add a message to the chat about the page switch
      const switchMessage: ChatMessage = {
        role: 'assistant',
        content: `✅ Switched to "${existingPage.name}" page`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, switchMessage]);

      return;
    }

    // Add a message to the chat about the new page creation
    const creationMessage: ChatMessage = {
      role: 'assistant',
      content: `🚀 Creating new "${pageName}" page...`,
      timestamp: new Date()
    };
    setMessages(prev => [...prev, creationMessage]);

    // Generate content for the new page with comprehensive navigation
    // This will auto-save the page after HTML generation completes
    const existingPageNames = pages.map(p => p.name).filter(name => name !== 'Main Page');
    const prompt = `Create a complete, production-grade ${pageName} page with:

🎯 **CRITICAL REQUIREMENTS:**
1. **Full Navigation Bar** - Include links to ALL existing pages: Home/Main Page${existingPageNames.length > 0 ? ', ' + existingPageNames.join(', ') : ''}
2. **Consistent Design** - Match the exact style, colors, and layout of the main page
3. **Professional Content** - High-quality, relevant content for "${pageName}"
4. **Responsive Layout** - Mobile-friendly design
5. **Interactive Elements** - Buttons, forms, or features appropriate for this page

🔗 **Navigation Requirements:**
- Navigation bar at the top with links to: Home, ${existingPageNames.join(', ')}${existingPageNames.length > 0 ? ', ' : ''}About, Contact
- Each nav link should be clickable (use <a> tags with proper text)
- Current page ("${pageName}") should be highlighted/active in navigation
- Consistent navigation styling across all pages

📄 **Content for ${pageName}:**
${pageName.toLowerCase().includes('about') ?
  '- Company/team information, mission, values, history, team members' :
  pageName.toLowerCase().includes('contact') ?
  '- Contact form, address, phone, email, social media links, map' :
  pageName.toLowerCase().includes('services') ?
  '- Service offerings, pricing, features, benefits' :
  pageName.toLowerCase().includes('sign up') || pageName.toLowerCase().includes('signup') || pageName.toLowerCase().includes('register') ?
  '- Registration form with fields like name, email, password, confirm password\n- Clear call-to-action buttons\n- Terms of service and privacy policy links\n- Social login options if appropriate\n- "Already have an account? Login" link' :
  pageName.toLowerCase().includes('login') || pageName.toLowerCase().includes('sign in') || pageName.toLowerCase().includes('signin') ?
  '- Login form with email/username and password fields\n- "Remember me" checkbox\n- "Forgot password?" link\n- "Don\'t have an account? Sign up" link\n- Social login options if appropriate' :
  pageName.toLowerCase().includes('dashboard') ?
  '- User dashboard with navigation menu\n- Key metrics or summary cards\n- Recent activity or notifications\n- Quick action buttons\n- User profile section' :
  pageName.toLowerCase().includes('pricing') ?
  '- Pricing tiers with features comparison\n- Monthly/yearly toggle\n- "Most popular" highlighting\n- Clear call-to-action buttons\n- FAQ section about pricing' :
  `- Relevant, professional content appropriate for "${pageName}"`
}

Create a complete, standalone HTML page that looks professional and matches the main page design exactly.`;

    console.log('🔥 Starting page generation with prompt:', prompt);
    await generatePageContent(prompt, pageId);

    // After creating new page, link all pages automatically
    setTimeout(() => {
      console.log('🔗 Triggering automatic page linking after new page creation');
      linkAllPages();
    }, 4000); // Increased delay to ensure page generation is complete
  };

  // Update main page navigation to include all created pages
  const updateMainPageNavigation = async () => {
    console.log('🔗 updateMainPageNavigation called');
    console.log('🔗 Total pages available:', pages.length);
    console.log('🔗 All pages:', pages.map(p => ({ id: p.id, name: p.name, hasContent: !!p.content })));

    // Skip if currently generating to avoid conflicts
    if (isGenerating) {
      console.log('🔗 Skipping navigation update - currently generating');
      return;
    }

    // Find main page - it could be 'main', 'main-page', or the first page
    const mainPageCandidates = pages.filter(p =>
      p.id === 'main' ||
      p.id === 'main-page' ||
      p.name.toLowerCase().includes('main') ||
      p.name.toLowerCase().includes('home')
    );

    console.log('🔗 Main page candidates:', mainPageCandidates.map(p => ({ id: p.id, name: p.name })));

    let mainPage = mainPageCandidates[0];
    if (!mainPage && pages.length > 0) {
      mainPage = pages[0]; // fallback to first page
      console.log('🔗 Using first page as main:', { id: mainPage.id, name: mainPage.name });
    }

    if (!mainPage) {
      console.log('🔗 No pages available at all');
      return;
    }

    if (!mainPage.content) {
      console.log('🔗 Main page has no content:', { id: mainPage.id, name: mainPage.name, contentLength: mainPage.content?.length || 0 });
      return;
    }

    const allPageNames = pages.filter(p => p.id !== mainPage.id).map(p => p.name);
    if (allPageNames.length === 0) {
      console.log('🔗 No additional pages to add to navigation');
      return;
    }

    console.log('🔗 Proceeding with navigation update:', {
      mainPageId: mainPage.id,
      mainPageName: mainPage.name,
      mainPageContentLength: mainPage.content.length,
      pagesToAdd: allPageNames
    });

    const prompt = `Update the navigation bar on this page to include links to all the following pages:
${allPageNames.map(name => `- ${name}`).join('\n')}

🎯 **CRITICAL REQUIREMENTS:**
1. **Keep all existing content and design exactly the same** - only modify navigation
2. **Add navigation links** for each page listed above
3. **Use proper <a> tags** with clickable text (e.g., <a href="#">About</a>)
4. **Maintain consistent styling** with existing navigation elements
5. **Do not change any other content** on the page - only the navigation bar
6. **If no navigation exists**, create a simple navigation bar at the top
7. **Make links clearly visible** and properly styled

**Navigation Format:**
- Use <a> tags for each page link
- Include "Home" or "Main" link for the current page
- Style consistently with the page design
- Position at the top of the page (header/nav area)

Add these navigation links while preserving everything else on the page exactly as it is.`;

    // Use edit endpoint to update main page
    console.log('🔗 Starting API call to update navigation');
    console.log('🔗 Request payload:', {
      htmlContentLength: mainPage.content.length,
      prompt: prompt.substring(0, 200) + '...'
    });

    setIsGenerating(true);
    try {
      const response = await fetch('/api/llm/v3/edit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          htmlContent: mainPage.content,
          prompt: prompt
        })
      });

      console.log('🔗 API response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('🔗 Failed to update main page navigation:', response.status, errorText);
        return;
      }

      console.log('🔗 API call successful, processing response...');

      const reader = response.body?.getReader();
      if (!reader) return;

      const decoder = new TextDecoder();
      let accumulatedContent = '';
      let isCollectingHTML = false;

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('event:')) {
            const event = line.substring(6);
            if (event === 'start') {
              isCollectingHTML = true;
            } else if (event === 'end') {
              isCollectingHTML = false;
              console.log('🔗 Processing end event, accumulated content length:', accumulatedContent.length);

              const cleanHtml = extractHtmlFromResponse(accumulatedContent);
              console.log('🔗 Clean HTML extracted, length:', cleanHtml.length);

              // Ensure complete HTML document
              let completeHtml = cleanHtml;
              if (!completeHtml.includes('<!DOCTYPE html')) {
                console.log('🔗 Adding DOCTYPE wrapper to HTML');
                completeHtml = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generated Content</title>
</head>
<body>
    ${completeHtml}
</body>
</html>`;
              }

              console.log('🔗 Adding interaction detection to updated content');
              const interactiveHtml = addInteractionDetection(completeHtml);
              console.log('🔗 Interactive HTML ready, length:', interactiveHtml.length);

              console.log('🔗 Updating pages array with new content');
              // Update main page content
              setPages(prev => prev.map(page =>
                page.id === mainPage.id
                  ? { ...page, content: interactiveHtml }
                  : page
              ));

              // If currently viewing main page, update iframe
              if (currentPageId === mainPage.id) {
                console.log('🔗 Updating iframe with new navigation content');
                setStableIframeContent(interactiveHtml);
                setHtmlContent(cleanHtml);
              } else {
                console.log('🔗 Not updating iframe (current page is not main page)');
              }

              console.log('🔗 Main page navigation updated successfully!');
            }
          } else if (line.startsWith('data:') && isCollectingHTML) {
            const data = line.substring(5);
            accumulatedContent += data;
          }
        }
      }
    } catch (error) {
      console.error('Error updating main page navigation:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  // Clean up duplicate pages
  const cleanupDuplicatePages = () => {
    console.log('🧹 Cleaning up duplicate pages...');

    setPages(prev => {
      const seen = new Set<string>();
      const uniquePages = prev.filter(page => {
        // Normalize page name for comparison
        const normalizedName = page.name.toLowerCase().trim();

        // Create a unique key based on normalized name
        const key = normalizedName
          .replace(/[^a-z0-9\s]/g, '')
          .replace(/\s+/g, '-');

        if (seen.has(key)) {
          console.log('🧹 Removing duplicate page:', page.name, page.id);
          return false; // Remove duplicate
        }

        seen.add(key);
        return true; // Keep unique page
      });

      console.log('🧹 Cleanup complete. Pages before:', prev.length, 'after:', uniquePages.length);
      return uniquePages;
    });
  };

  // Link all pages with navigation - improved version
  const linkAllPages = async () => {
    console.log('🔗 linkAllPages called');

    if (isGenerating || isLinking) {
      console.log('🔗 Skipping - currently generating or linking');
      return;
    }

    const pagesWithContent = pages.filter(p => p.content && p.content.length > 0);
    console.log('🔗 Pages with content:', pagesWithContent.map(p => ({ id: p.id, name: p.name })));

    if (pagesWithContent.length < 2) {
      console.log('🔗 Need at least 2 pages with content to link');
      return;
    }

    setIsLinking(true);

    try {
      // Update each page to include navigation to all other pages
      for (const page of pagesWithContent) {
      const otherPageNames = pagesWithContent
        .filter(p => p.id !== page.id)
        .map(p => p.name);

      if (otherPageNames.length === 0) continue;

      console.log(`🔗 Updating navigation for ${page.name} to include:`, otherPageNames);

      const prompt = `Update the navigation bar on this page to include links to all the following pages:
${otherPageNames.map(name => `- ${name}`).join('\n')}

🎯 **CRITICAL REQUIREMENTS:**
1. **Keep all existing content and design exactly the same** - only modify navigation
2. **Add navigation links** for each page listed above
3. **Use proper <a> tags** with clickable text (e.g., <a href="#">About</a>)
4. **Maintain consistent styling** with existing navigation elements
5. **Do not change any other content** on the page - only the navigation bar
6. **If no navigation exists**, create a simple navigation bar at the top
7. **Make links clearly visible** and properly styled

**Navigation Format:**
- Use <a> tags for each page link
- Include "Home" or "Main" link if this isn't the main page
- Style consistently with the page design
- Position at the top of the page (header/nav area)

Add these navigation links while preserving everything else on the page exactly as it is.`;

      try {
        setIsGenerating(true);
        const response = await fetch('/api/llm/v3/edit', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            htmlContent: page.content,
            prompt: prompt
          })
        });

        if (!response.ok) {
          console.error(`🔗 Failed to update navigation for ${page.name}:`, response.status);
          continue;
        }

        const reader = response.body?.getReader();
        if (!reader) continue;

        const decoder = new TextDecoder();
        let accumulatedContent = '';
        let isCollectingHTML = false;

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('event:')) {
              const event = line.substring(6);
              if (event === 'start') {
                isCollectingHTML = true;
              } else if (event === 'end') {
                isCollectingHTML = false;
                const cleanHtml = extractHtmlFromResponse(accumulatedContent);

                // Ensure complete HTML document
                let completeHtml = cleanHtml;
                if (!completeHtml.includes('<!DOCTYPE html')) {
                  completeHtml = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generated Content</title>
</head>
<body>
    ${completeHtml}
</body>
</html>`;
                }

                const interactiveHtml = addInteractionDetection(completeHtml);

                // Update the page content
                setPages(prev => prev.map(p =>
                  p.id === page.id
                    ? { ...p, content: interactiveHtml }
                    : p
                ));

                // If this is the current page, update iframe
                if (page.id === currentPageId) {
                  setStableIframeContent(interactiveHtml);
                  setHtmlContent(cleanHtml);
                }

                console.log(`🔗 Updated navigation for ${page.name}`);
              }
            } else if (line.startsWith('data:') && isCollectingHTML) {
              const data = line.substring(5);
              accumulatedContent += data;
            }
          }
        }
      } catch (error) {
        console.error(`🔗 Error updating navigation for ${page.name}:`, error);
      } finally {
        setIsGenerating(false);
      }

      // Small delay between updates to avoid overwhelming the API
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

      console.log('🔗 All pages linked successfully!');
    } catch (error) {
      console.error('🔗 Error during page linking:', error);
    } finally {
      setIsLinking(false);
    }
  };

  // Handle new page creation
  const handleNewPage = () => {
    const pageName = `New Page ${pages.length}`;

    // Set input and trigger form submission - this will use the auto-save mechanism
    setInput(`Create a new ${pageName} with navigation back to the main page`);

    // Trigger the form submission
    const form = document.querySelector('form');
    if (form) {
      form.dispatchEvent(new Event('submit', { bubbles: true, cancelable: true }));
    }
  };

  // Generate content for a specific page
  const generatePageContent = async (prompt: string, pageId: string) => {
    setIsGenerating(true);
    setStreamingContent('');

    try {
      const response = await fetch('/api/llm/v3/generate-html', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ prompt })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error:', response.status, errorText);
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body');
      }

      const decoder = new TextDecoder();
      let accumulatedContent = '';
      let isCollectingHTML = false;

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('event:')) {
            const event = line.substring(6);
            if (event === 'start') {
              isCollectingHTML = true;
            } else if (event === 'end') {
              isCollectingHTML = false;
              const cleanHtml = extractHtmlFromResponse(accumulatedContent);
              console.log('Page generation complete:', cleanHtml.substring(0, 100) + '...');

              // Ensure complete HTML document
              let completeHtml = cleanHtml;
              if (!completeHtml.includes('<!DOCTYPE html')) {
                completeHtml = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generated Content</title>
</head>
<body>
    ${completeHtml}
</body>
</html>`;
              }

              const interactiveHtml = addInteractionDetection(completeHtml);
              setHtmlContent(cleanHtml);
              setStableIframeContent(interactiveHtml);

              // Update the specific page content
              setPages(prev => {
                const updated = prev.map(page =>
                  page.id === pageId
                    ? { ...page, content: interactiveHtml }
                    : page
                );

                console.log('🔥 Page content updated for:', pageId);
                console.log('🔥 Updated pages:', updated.map(p => ({ id: p.id, name: p.name, hasContent: !!p.content })));

                // Trigger automatic linking after page content is set
                setTimeout(() => {
                  console.log('🔗 Triggering automatic page linking after page generation');
                  linkAllPages();
                }, 3000); // Delay to ensure state is updated

                return updated;
              });

              setStreamingContent('');
            }
          } else if (line.startsWith('data:') && isCollectingHTML) {
            const data = line.substring(5);
            accumulatedContent += data;
            setStreamingContent(accumulatedContent);
          }
        }
      }

    } catch (error) {
      console.error('Error generating page:', error);
    } finally {
      setIsGenerating(false);
    }
  };



  return (
    <>
      {/* Fixed Progress Indicator at Top - Always visible when generating */}
      {isGenerating && (
        <div className="fixed top-0 left-0 right-0 bg-blue-600 text-white shadow-lg z-[9999]">
          <div className="px-6 py-3">
            <div className="flex items-center justify-center space-x-4">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-white rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
              <span className="text-white font-medium">🤖 AI is generating your prototype...</span>
            </div>
          </div>
        </div>
      )}

      <div className={`h-screen flex flex-col bg-gray-50 ${isGenerating ? 'pt-16' : ''}`}>
      {/* Top Toolbar */}
      <div className="bg-white border-b border-gray-200 px-6 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-lg font-semibold text-gray-900">Design Editor</h1>
            <div className="flex items-center bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('preview')}
                className={`px-3 py-1.5 rounded-md text-sm font-medium transition-all ${
                  viewMode === 'preview'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <FiEye className="inline mr-1.5" />
                Preview
              </button>
              <button
                onClick={() => setViewMode('code')}
                className={`px-3 py-1.5 rounded-md text-sm font-medium transition-all ${
                  viewMode === 'code'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <FiCode className="inline mr-1.5" />
                Code
              </button>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <div className="text-sm text-gray-600">
              Current: <span className="font-medium text-gray-900">{pages.find(p => p.id === currentPageId)?.name || 'Main Page'}</span>
              {isLinking && (
                <span className="ml-2 text-xs text-blue-600 font-medium">
                  🔗 Linking pages...
                </span>
              )}
              {isGenerating && (
                <span className="ml-2 text-xs text-orange-600 font-bold bg-orange-100 px-2 py-1 rounded">
                  🤖 GENERATING...
                </span>
              )}
            </div>

            <button className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
              Export
            </button>
            {/* Share Button - Only show if we have content */}
            {(htmlContent || stableIframeContent) && (
              <ShareButton
                prototypeId={currentPageId || 'current-page'}
                prototypeName={pages.find(p => p.id === currentPageId)?.name || 'My Prototype'}
                variant="secondary"
                size="medium"
              />
            )}
            <button className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors">
              Save
            </button>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex overflow-hidden">
        {/* Page Navigation Sidebar - Like Readdy.ai */}
        <div className="w-64 bg-gray-50 border-r border-gray-200 flex flex-col">
          <div className="p-4 border-b border-gray-200">
            <h3 className="text-sm font-medium text-gray-900 mb-3">Pages</h3>
            <button
              onClick={handleNewPage}
              className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-blue-700 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              <span>New Page</span>
            </button>
          </div>

          <div className="flex-1 overflow-y-auto p-2">
            {pages.map((page) => (
              <button
                key={page.id}
                onClick={() => {
                  console.log('🔄 Switching to page:', page.name, page.id);
                  console.log('🔄 Page content length:', page.content?.length || 0);

                  setCurrentPageId(page.id);

                  // Update iframe content and other states
                  if (page.content) {
                    console.log('🔄 Setting page content for:', page.name);
                    setStableIframeContent(page.content);
                    setHtmlContent(page.content);

                    // Add a message to the chat about the page switch
                    const switchMessage: ChatMessage = {
                      role: 'assistant',
                      content: `📄 Switched to "${page.name}" page`,
                      timestamp: new Date()
                    };
                    setMessages(prev => [...prev, switchMessage]);
                  } else {
                    console.log('🔄 No content for page:', page.name, '- clearing iframe');
                    setStableIframeContent('');
                    setHtmlContent('');

                    // Add a message about empty page
                    const emptyMessage: ChatMessage = {
                      role: 'assistant',
                      content: `📄 Switched to "${page.name}" page (empty - ready for content)`,
                      timestamp: new Date()
                    };
                    setMessages(prev => [...prev, emptyMessage]);
                  }
                }}
                className={`w-full text-left px-3 py-2 mb-1 rounded-lg text-sm transition-colors ${
                  currentPageId === page.id
                    ? 'bg-blue-100 text-blue-900 border border-blue-200'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <span className="truncate">{page.name}</span>
                  {page.content && (
                    <span className="ml-auto text-xs text-green-600">✓</span>
                  )}
                </div>
              </button>
            ))}

            {/* Debug info */}
            <div className="mt-4 p-2 bg-gray-50 rounded text-xs text-gray-600">
              <div>Total pages: {pages.length}</div>
              <div>Current: {currentPageId}</div>
              <div>Pages with content: {pages.filter(p => p.content).length}</div>
              <div className="mt-1 space-y-1">
                {pages.map(p => (
                  <div key={p.id} className={`${p.id === currentPageId ? 'font-bold text-blue-600' : ''}`}>
                    {p.name} ({p.id}) {p.content ? '✅' : '❌'} {p.content?.length || 0} chars
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Preview Panel - CENTER */}
        <div className="flex-1 flex flex-col bg-white">
          {/* Preview Content */}
          <div className="flex-1 relative">
            {viewMode === 'preview' ? (
              isGenerating ? (
                <div className="flex items-center justify-center h-full bg-gray-50">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">AI is thinking...</h3>
                    <p className="text-gray-600 max-w-sm">Generating your design</p>
                    {streamingContent && (
                      <div className="mt-4 p-3 bg-blue-50 rounded-lg max-w-md mx-auto">
                        <p className="text-xs text-blue-700 font-mono truncate">{streamingContent.substring(0, 50)}...</p>
                      </div>
                    )}
                  </div>
                </div>
              ) : stableIframeContent ? (
                <div className="h-full bg-gray-100 p-8">
                  <div className="h-full bg-white rounded-lg shadow-sm overflow-hidden">
                    <iframe
                      ref={iframeRef}
                      srcDoc={stableIframeContent}
                      className="w-full h-full border-0"
                      title="Preview"
                      sandbox="allow-scripts allow-same-origin allow-popups allow-forms"
                    />
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-full bg-gray-50">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                      <FiEye className="text-2xl text-gray-400" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Ready to create</h3>
                    <p className="text-gray-600 max-w-sm">Start a conversation with the AI assistant to generate your design</p>
                  </div>
                </div>
              )
            ) : (
              <div className="h-full bg-gray-900 text-gray-100">
                {formattedCodeContent ? (
                  <div className="h-full overflow-auto">
                    <div className="p-6">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-sm font-medium text-gray-300">Generated HTML</h3>
                        <button
                          onClick={() => navigator.clipboard.writeText(cleanHtmlContent)}
                          className="px-3 py-1 text-xs bg-gray-800 text-gray-300 rounded hover:bg-gray-700 transition-colors"
                        >
                          Copy Code
                        </button>
                      </div>
                      <pre className="text-sm leading-relaxed font-mono bg-gray-800 rounded-lg p-4 overflow-x-auto">
                        <code className="text-gray-100 whitespace-pre">{formattedCodeContent}</code>
                      </pre>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <div className="w-16 h-16 bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
                        <FiCode className="text-2xl text-gray-400" />
                      </div>
                      <h3 className="text-lg font-medium text-gray-100 mb-2">Code view</h3>
                      <p className="text-gray-400 max-w-sm">Generated code will appear here</p>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* AI Assistant Panel - RIGHT SIDE like Readdy.ai */}
        <div className="w-96 bg-white border-l border-gray-200 flex flex-col">
          {/* Assistant Header */}
          <div className="p-4 border-b border-gray-100">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">AI</span>
              </div>
              <div>
                <h2 className="font-medium text-gray-900">Design Assistant</h2>
                <p className="text-xs text-gray-500">Powered by GPT-4</p>
              </div>
            </div>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.map((message, index) => (
              <div
                key={index}
                className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                {message.type === 'plan' ? (
                  // Plan message with Readdy.ai-style formatting
                  <div className="max-w-[95%] bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                    <div className="flex items-center space-x-2 mb-4">
                      <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                        <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      </div>
                      <span className="text-base font-semibold text-gray-900">Project Plan</span>
                    </div>
                    <div className="prose prose-sm max-w-none">
                      <div
                        className="text-gray-700 leading-relaxed"
                        dangerouslySetInnerHTML={{
                          __html: formatPlanContent(message.content)
                        }}
                      />
                    </div>
                    <p className="text-xs text-gray-500 mt-4 pt-3 border-t border-gray-100">
                      {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </p>
                  </div>
                ) : (
                  // Regular message with improved styling
                  <div
                    className={`max-w-[85%] rounded-lg px-4 py-3 ${
                      message.role === 'user'
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-50 text-gray-900 border border-gray-200'
                    }`}
                  >
                    <p className="text-sm leading-relaxed whitespace-pre-wrap">{message.content}</p>
                    <p className={`text-xs mt-2 ${
                      message.role === 'user' ? 'text-blue-100' : 'text-gray-500'
                    }`}>
                      {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </p>
                  </div>
                )}
              </div>
            ))}



            <div ref={messagesEndRef} />
          </div>

          {/* Input Area */}
          <div className="p-4 border-t border-gray-100">
            <form onSubmit={handleSubmit} className="space-y-3">
              <div className="relative">
                <input
                  type="text"
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  placeholder={
                    (htmlContent || stableIframeContent)
                      ? "Describe what you want to change..."
                      : "Describe what you want to create..."
                  }
                  className="w-full px-4 py-3 pr-12 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm resize-none"
                  disabled={isGenerating}
                />
                <button
                  type="submit"
                  disabled={!input.trim() || isGenerating}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-colors"
                >
                  {isGenerating ? (
                    <FiLoader className="w-4 h-4 animate-spin" />
                  ) : (
                    <FiSend className="w-4 h-4" />
                  )}
                </button>
              </div>

              {/* Quick Actions */}
              <div className="flex flex-wrap gap-2">
                {(htmlContent || stableIframeContent) ? (
                  // Edit mode quick actions
                  <>
                    <button
                      type="button"
                      onClick={() => setInput("Make it more modern")}
                      className="px-3 py-1.5 text-xs bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                      disabled={isGenerating}
                    >
                      Make it modern
                    </button>
                    <button
                      type="button"
                      onClick={() => setInput("Change the colors")}
                      className="px-3 py-1.5 text-xs bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                      disabled={isGenerating}
                    >
                      Change colors
                    </button>
                    <button
                      type="button"
                      onClick={() => setInput("Add animations")}
                      className="px-3 py-1.5 text-xs bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                      disabled={isGenerating}
                    >
                      Add animations
                    </button>
                  </>
                ) : (
                  // New generation quick actions
                  <>
                    <button
                      type="button"
                      onClick={() => setInput("Create a landing page")}
                      className="px-3 py-1.5 text-xs bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
                      disabled={isGenerating}
                    >
                      Landing page
                    </button>
                    <button
                      type="button"
                      onClick={() => setInput("Create a dashboard")}
                      className="px-3 py-1.5 text-xs bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
                      disabled={isGenerating}
                    >
                      Dashboard
                    </button>
                    <button
                      type="button"
                      onClick={() => setInput("Create a contact form")}
                      className="px-3 py-1.5 text-xs bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
                      disabled={isGenerating}
                    >
                      Contact form
                    </button>
                  </>
                )}
              </div>
            </form>
          </div>
        </div>
      </div>

      {/* Implementation Modal - Readdy.ai Style */}
      {showImplementModal && selectedElement && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-2xl max-w-md w-full mx-4">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Implement Feature</h3>
                <button
                  onClick={() => setShowImplementModal(false)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="mb-4">
                <div className="bg-gray-50 rounded-lg p-3 mb-3">
                  <p className="text-sm text-gray-600 mb-1">Selected Element:</p>
                  <p className="font-medium text-gray-900">{selectedElement.textContent}</p>
                  <p className="text-xs text-gray-500">{selectedElement.tagName.toLowerCase()}</p>
                </div>
              </div>

              <div className="space-y-3">
                <button
                  onClick={() => handleImplementFeature('modal')}
                  className="w-full flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                      <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                    <div className="text-left">
                      <p className="font-medium text-gray-900">Add as Modal</p>
                      <p className="text-sm text-gray-600">Implement as popup/modal</p>
                    </div>
                  </div>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>

                <button
                  onClick={() => handleImplementFeature('page')}
                  className="w-full flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                      <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <div className="text-left">
                      <p className="font-medium text-gray-900">Create New Page</p>
                      <p className="text-sm text-gray-600">Navigate to separate page</p>
                    </div>
                  </div>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>

                <button
                  onClick={() => handleImplementFeature('inline')}
                  className="w-full flex items-center justify-between p-3 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                      <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </div>
                    <div className="text-left">
                      <p className="font-medium text-gray-900">Implement Inline</p>
                      <p className="text-sm text-gray-600">Add functionality in place</p>
                    </div>
                  </div>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
    </>
  );
}
