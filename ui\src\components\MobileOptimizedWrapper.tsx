/**
 * Mobile Optimized Wrapper Component
 * 
 * This component wraps content with mobile-specific optimizations including
 * touch gestures, responsive behavior, and mobile-friendly interactions.
 */

import React, { useEffect, useRef, useState } from 'react';
import { 
  DeviceUtils, 
  TouchGestureHandler, 
  MobileNavUtils, 
  ViewportUtils,
  initializeMobileFeatures,
  type SwipeEvent 
} from '../utils/mobileUtils';

interface MobileOptimizedWrapperProps {
  children: React.ReactNode;
  enableSwipeGestures?: boolean;
  enableMobileNav?: boolean;
  className?: string;
  onSwipe?: (event: SwipeEvent) => void;
  onViewportChange?: (size: { width: number; height: number }) => void;
}

const MobileOptimizedWrapper: React.FC<MobileOptimizedWrapperProps> = ({
  children,
  enableSwipeGestures = true,
  enableMobileNav = true,
  className = '',
  onSwipe,
  onViewportChange
}) => {
  const wrapperRef = useRef<HTMLDivElement>(null);
  const gestureHandlerRef = useRef<TouchGestureHandler | null>(null);
  const [isMobile, setIsMobile] = useState(DeviceUtils.isMobile());
  const [isLandscape, setIsLandscape] = useState(DeviceUtils.isLandscape());

  // Initialize mobile features on mount
  useEffect(() => {
    initializeMobileFeatures();
  }, []);

  // Set up touch gesture handling
  useEffect(() => {
    if (enableSwipeGestures && wrapperRef.current && DeviceUtils.isTouchDevice()) {
      gestureHandlerRef.current = new TouchGestureHandler(wrapperRef.current, {
        minSwipeDistance: 50,
        maxSwipeTime: 1000,
        preventDefaultScroll: false
      });

      gestureHandlerRef.current.setOnSwipe((event: SwipeEvent) => {
        // Handle navigation swipes
        if (enableMobileNav && isMobile) {
          if (event.direction === 'right' && event.startX < 50) {
            // Swipe right from left edge - open nav
            MobileNavUtils.openMobileNav();
          } else if (event.direction === 'left' && MobileNavUtils.isMobileNavOpen()) {
            // Swipe left when nav is open - close nav
            MobileNavUtils.closeMobileNav();
          }
        }

        // Call custom swipe handler
        if (onSwipe) {
          onSwipe(event);
        }
      });

      return () => {
        if (gestureHandlerRef.current) {
          gestureHandlerRef.current.destroy();
        }
      };
    }
  }, [enableSwipeGestures, enableMobileNav, isMobile, onSwipe]);

  // Set up viewport change handling
  useEffect(() => {
    const cleanup = ViewportUtils.onViewportChange((size) => {
      const newIsMobile = size.width < 768;
      const newIsLandscape = size.width > size.height;
      
      setIsMobile(newIsMobile);
      setIsLandscape(newIsLandscape);

      // Close mobile nav when switching to desktop
      if (!newIsMobile && MobileNavUtils.isMobileNavOpen()) {
        MobileNavUtils.closeMobileNav();
      }

      if (onViewportChange) {
        onViewportChange(size);
      }
    });

    return cleanup;
  }, [onViewportChange]);

  // Handle escape key to close mobile nav
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && MobileNavUtils.isMobileNavOpen()) {
        MobileNavUtils.closeMobileNav();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  const wrapperClasses = [
    'mobile-optimized-wrapper',
    'relative',
    'min-h-screen',
    'overflow-x-hidden',
    className
  ].filter(Boolean).join(' ');

  return (
    <div 
      ref={wrapperRef}
      className={wrapperClasses}
      data-mobile={isMobile}
      data-landscape={isLandscape}
      data-touch-device={DeviceUtils.isTouchDevice()}
    >
      {/* Mobile Navigation Overlay */}
      {enableMobileNav && (
        <>
          <div 
            id="mobileNavOverlay" 
            className="fixed inset-0 bg-black bg-opacity-50 z-40 hidden transition-opacity duration-300"
            onClick={() => MobileNavUtils.closeMobileNav()}
            aria-hidden="true"
          />

          {/* Mobile Navigation Drawer */}
          <nav 
            id="mobileNav"
            className="fixed top-0 left-0 h-full w-80 max-w-[80vw] bg-white shadow-xl z-50 transform -translate-x-full transition-transform duration-300 ease-in-out"
            aria-label="Mobile navigation"
          >
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-gray-800">Navigation</h2>
                <button 
                  className="p-2 rounded-lg hover:bg-gray-100 transition-colors touch-manipulation"
                  onClick={() => MobileNavUtils.closeMobileNav()}
                  aria-label="Close navigation"
                  style={{ minHeight: '44px', minWidth: '44px' }}
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
            <div 
              id="mobileNavContent" 
              className="p-4 overflow-y-auto h-full pb-20"
              style={{ WebkitOverflowScrolling: 'touch' }}
            >
              {/* Navigation content will be populated dynamically */}
              <div className="space-y-2">
                <p className="text-sm text-gray-600 mb-4">
                  Navigation items will appear here based on the current page content.
                </p>
              </div>
            </div>
          </nav>

          {/* Mobile Navigation Toggle Button */}
          <button 
            id="mobileNavToggle"
            className="fixed top-4 left-4 z-30 p-3 bg-white rounded-lg shadow-lg border border-gray-200 md:hidden touch-manipulation"
            onClick={() => MobileNavUtils.openMobileNav()}
            aria-label="Open navigation menu"
            style={{ minHeight: '44px', minWidth: '44px' }}
          >
            <svg className="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </>
      )}

      {/* Main Content */}
      <main className="relative z-10">
        {children}
      </main>

      {/* Mobile-specific styles */}
      <style jsx>{`
        .mobile-optimized-wrapper {
          /* Prevent horizontal scrolling */
          overflow-x: hidden;
          
          /* Optimize touch scrolling */
          -webkit-overflow-scrolling: touch;
          overscroll-behavior: contain;
        }

        /* Touch-friendly interactive elements */
        .mobile-optimized-wrapper button,
        .mobile-optimized-wrapper a,
        .mobile-optimized-wrapper input,
        .mobile-optimized-wrapper select,
        .mobile-optimized-wrapper textarea {
          touch-action: manipulation;
          min-height: 44px;
          min-width: 44px;
        }

        /* Improve tap targets on mobile */
        @media (max-width: 767px) {
          .mobile-optimized-wrapper button,
          .mobile-optimized-wrapper a {
            padding: 0.75rem 1rem;
          }
          
          .mobile-optimized-wrapper input,
          .mobile-optimized-wrapper select,
          .mobile-optimized-wrapper textarea {
            padding: 0.75rem;
            font-size: 16px; /* Prevent zoom on iOS */
          }
        }

        /* Safe area support for devices with notches */
        .mobile-optimized-wrapper {
          padding-top: env(safe-area-inset-top);
          padding-left: env(safe-area-inset-left);
          padding-right: env(safe-area-inset-right);
          padding-bottom: env(safe-area-inset-bottom);
        }

        /* Mobile navigation styles */
        #mobileNav {
          /* Ensure nav is above other content */
          z-index: 50;
          
          /* Smooth scrolling */
          scroll-behavior: smooth;
        }

        #mobileNavContent {
          /* Touch-friendly scrolling */
          -webkit-overflow-scrolling: touch;
          overscroll-behavior: contain;
        }

        /* Hide mobile nav toggle on desktop */
        @media (min-width: 768px) {
          #mobileNavToggle {
            display: none !important;
          }
        }

        /* Landscape mode adjustments */
        @media (orientation: landscape) and (max-height: 500px) {
          #mobileNav {
            width: 60vw;
            max-width: 400px;
          }
          
          #mobileNavToggle {
            top: 0.5rem;
            left: 0.5rem;
            padding: 0.5rem;
          }
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
          #mobileNav {
            border: 2px solid;
          }
          
          #mobileNavToggle {
            border: 2px solid;
          }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
          #mobileNav,
          #mobileNavOverlay {
            transition: none !important;
          }
        }

        /* Focus indicators for accessibility */
        .mobile-optimized-wrapper button:focus,
        .mobile-optimized-wrapper a:focus {
          outline: 2px solid #3b82f6;
          outline-offset: 2px;
        }
      `}</style>
    </div>
  );
};

export default MobileOptimizedWrapper;
