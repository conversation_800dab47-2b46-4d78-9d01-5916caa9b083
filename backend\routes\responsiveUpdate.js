/**
 * Responsive Update API Routes
 * 
 * Provides API endpoints for updating pages with responsive design improvements
 */

const express = require('express');
const router = express.Router();
const responsiveUpdateService = require('../services/responsiveUpdateService');
const { ensureAuthenticated } = require('../middleware/auth');

/**
 * GET /api/responsive-update/stats
 * Get statistics about responsive design status across all pages
 */
router.get('/stats', ensureAuthenticated, async (req, res) => {
  try {
    console.log('📊 [ResponsiveUpdateAPI] Getting responsive design statistics');
    
    const stats = await responsiveUpdateService.getUpdateStatistics();
    
    res.json({
      success: true,
      stats: {
        totalPages: stats.totalPages,
        responsivePages: stats.responsivePages,
        nonResponsivePages: stats.nonResponsivePages,
        totalPrototypes: stats.totalPrototypes,
        responsivePercentage: stats.totalPages > 0 ? 
          Math.round((stats.responsivePages / stats.totalPages) * 100) : 0,
        pagesByPrototype: stats.pagesByPrototype
      }
    });
    
  } catch (error) {
    console.error('❌ [ResponsiveUpdateAPI] Error getting statistics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get responsive design statistics',
      details: error.message
    });
  }
});

/**
 * POST /api/responsive-update/preview
 * Preview what changes would be made to pages (dry run)
 */
router.post('/preview', ensureAuthenticated, async (req, res) => {
  try {
    const { batchSize = 50 } = req.body;
    
    console.log('🔍 [ResponsiveUpdateAPI] Running responsive update preview (dry run)');
    
    const results = await responsiveUpdateService.updateAllPages({
      dryRun: true,
      batchSize: parseInt(batchSize)
    });
    
    res.json({
      success: true,
      preview: {
        totalPages: results.totalPages,
        pagesNeedingUpdate: results.updatedPages,
        pagesAlreadyResponsive: results.skippedPages,
        errors: results.errors,
        estimatedDuration: `${((results.endTime - results.startTime) / 1000).toFixed(2)} seconds`
      }
    });
    
  } catch (error) {
    console.error('❌ [ResponsiveUpdateAPI] Error running preview:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to run responsive update preview',
      details: error.message
    });
  }
});

/**
 * POST /api/responsive-update/execute
 * Execute responsive design updates on all pages
 */
router.post('/execute', ensureAuthenticated, async (req, res) => {
  try {
    const { batchSize = 50, confirm = false } = req.body;
    
    if (!confirm) {
      return res.status(400).json({
        success: false,
        error: 'Confirmation required',
        message: 'Set confirm: true to execute responsive updates'
      });
    }
    
    console.log('🚀 [ResponsiveUpdateAPI] Executing responsive design updates');
    
    const results = await responsiveUpdateService.updateAllPages({
      dryRun: false,
      batchSize: parseInt(batchSize)
    });
    
    const duration = (results.endTime - results.startTime) / 1000;
    
    res.json({
      success: true,
      results: {
        totalPages: results.totalPages,
        updatedPages: results.updatedPages,
        skippedPages: results.skippedPages,
        errors: results.errors,
        duration: `${duration.toFixed(2)} seconds`,
        summary: {
          successful: results.errors.length === 0,
          updatedPercentage: results.totalPages > 0 ? 
            Math.round((results.updatedPages / results.totalPages) * 100) : 0
        }
      }
    });
    
  } catch (error) {
    console.error('❌ [ResponsiveUpdateAPI] Error executing updates:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to execute responsive design updates',
      details: error.message
    });
  }
});

/**
 * POST /api/responsive-update/single-page
 * Update a single page with responsive design improvements
 */
router.post('/single-page', ensureAuthenticated, async (req, res) => {
  try {
    const { pageId, dryRun = false } = req.body;
    
    if (!pageId) {
      return res.status(400).json({
        success: false,
        error: 'Page ID is required'
      });
    }
    
    const userId = req.user.dbId || req.user.id;
    
    console.log(`🔍 [ResponsiveUpdateAPI] ${dryRun ? 'Previewing' : 'Updating'} single page ${pageId}`);
    
    // Get the page first to verify ownership
    const prototypePageService = require('../services/prototypePageService');
    const page = await prototypePageService.getPageById(pageId, userId);
    
    if (!page) {
      return res.status(404).json({
        success: false,
        error: 'Page not found or access denied'
      });
    }
    
    // Add prototype title for context
    const prototypeService = require('../services/prototypeService');
    const prototype = await prototypeService.getPrototypeById(page.prototype_id, userId);
    page.prototype_title = prototype?.title || 'Unknown';
    page.user_id = userId;
    
    const result = await responsiveUpdateService.updateSinglePage(page, dryRun);
    
    res.json({
      success: true,
      result: {
        pageId: result.pageId,
        updated: result.updated,
        changes: result.changes,
        originalSize: result.originalSize,
        newSize: result.newSize,
        sizeChange: result.newSize - result.originalSize,
        pageTitle: page.title,
        prototypeTitle: page.prototype_title
      }
    });
    
  } catch (error) {
    console.error('❌ [ResponsiveUpdateAPI] Error updating single page:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update page with responsive design',
      details: error.message
    });
  }
});

/**
 * GET /api/responsive-update/health
 * Health check endpoint for the responsive update service
 */
router.get('/health', async (req, res) => {
  try {
    // Test database connection and service availability
    const stats = await responsiveUpdateService.getUpdateStatistics();
    
    res.json({
      success: true,
      status: 'healthy',
      service: 'responsive-update',
      timestamp: new Date().toISOString(),
      databaseConnected: true,
      totalPages: stats.totalPages
    });
    
  } catch (error) {
    console.error('❌ [ResponsiveUpdateAPI] Health check failed:', error);
    res.status(503).json({
      success: false,
      status: 'unhealthy',
      service: 'responsive-update',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

module.exports = router;
