import React, { useEffect, useState, useRef } from 'react';
import { useParams } from 'react-router-dom';
import { FiLoader, FiShare2, FiExternalLink } from 'react-icons/fi';

interface SharedPrototype {
  id: string;
  title: string;
  html: string;
  description?: string;
  createdAt: string;
  ownerName?: string;
}

const SharedPrototypePage: React.FC = () => {
  const { hash } = useParams<{ hash: string }>();
  const [prototype, setPrototype] = useState<SharedPrototype | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    console.log('🔗 SharedPrototypePage mounted with hash:', hash);

    if (!hash) {
      console.log('🔗 No hash provided, showing error');
      setError('Invalid share link');
      setLoading(false);
      return;
    }

    const fetchSharedPrototype = async () => {
      try {
        setLoading(true);
        setError(null);

        const API_BASE = import.meta.env.VITE_API_BASE_URL || '/api';
        const url = `${API_BASE}/share/access/${hash}`;

        console.log('🔗 Fetching shared prototype:', {
          hash,
          API_BASE,
          url,
          env: import.meta.env.VITE_API_BASE_URL
        });

        const response = await fetch(url);

        console.log('🔗 Response status:', response.status);
        console.log('🔗 Response headers:', Object.fromEntries(response.headers.entries()));

        if (!response.ok) {
          const errorText = await response.text();
          console.log('🔗 Error response body:', errorText);

          if (response.status === 404) {
            throw new Error('Shared prototype not found or link has expired');
          }
          throw new Error(`Failed to load shared prototype: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log('🔗 Shared prototype data:', data);

        setPrototype(data.prototype || data);
      } catch (err: any) {
        console.error('🔗 Error fetching shared prototype:', err);
        setError(err.message || 'Failed to load shared prototype');
      } finally {
        setLoading(false);
      }
    };

    fetchSharedPrototype();
  }, [hash]);

  // Update iframe when prototype changes
  useEffect(() => {
    if (prototype && iframeRef.current) {
      // Create a complete HTML document for the iframe
      const fullHtml = `
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${prototype.title}</title>
          <script src="https://cdn.tailwindcss.com"></script>
          <style>
            body { margin: 0; padding: 0; font-family: system-ui, -apple-system, sans-serif; }
          </style>
        </head>
        <body>
          ${prototype.html}
        </body>
        </html>
      `;

      iframeRef.current.srcdoc = fullHtml;
    }
  }, [prototype]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <FiLoader className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading shared prototype...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <FiShare2 className="w-8 h-8 text-red-600" />
          </div>
          <h1 className="text-xl font-semibold text-gray-900 mb-2">Unable to Load Prototype</h1>
          <p className="text-gray-600 mb-4">{error}</p>
          <a
            href="/"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <FiExternalLink className="w-4 h-4 mr-2" />
            Go to JustPrototype
          </a>
        </div>
      </div>
    );
  }

  if (!prototype) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">No prototype data found</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-semibold text-gray-900">{prototype.title}</h1>
            {prototype.description && (
              <p className="text-sm text-gray-600 mt-1">{prototype.description}</p>
            )}
          </div>
          <div className="flex items-center space-x-3">
            <span className="text-sm text-gray-500">
              Shared prototype
            </span>
            <a
              href="/"
              className="inline-flex items-center px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <FiExternalLink className="w-4 h-4 mr-2" />
              Create Your Own
            </a>
          </div>
        </div>
      </div>

      {/* Prototype Content */}
      <div className="flex-1 p-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-[calc(100vh-140px)]">
          <iframe
            ref={iframeRef}
            className="w-full h-full rounded-lg"
            title={prototype.title}
            sandbox="allow-scripts allow-same-origin allow-forms"
          />
        </div>
      </div>
    </div>
  );
};

export default SharedPrototypePage;
