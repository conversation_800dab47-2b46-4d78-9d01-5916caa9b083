# Page List Refresh Debug Guide

## Issues Fixed

### 1. **Page List Refresh Timing**
**Problem**: New pages weren't appearing in PageSidebar after creation.

**Root Causes Identified**:
- Event listener dependency array included frequently changing values, causing re-registration
- Page list refresh was happening after page selection, potentially causing race conditions
- No delay to allow API consistency before refresh

**Fixes Applied**:
- Fixed event listener dependency array to only include `projectId`
- Added 500ms delay before refreshing page list to ensure API consistency
- Moved page list refresh to happen BEFORE page selection
- Added comprehensive debugging logs

### 2. **Data Flow Verification**
**Enhanced Debugging**:
- Added detailed logging in `refreshProjectPages()` function
- Added tracking of `projectPages` state changes
- Added logging of API response data structure
- Added timestamp tracking for debugging timing issues

### 3. **Event Handler Sequence**
**Improved Flow**:
```
Page Save Event → 
500ms Delay → 
Refresh Page List → 
Set Current Session ID → 
Load Page Content → 
Update Editor State → 
Switch to Page
```

## Debug Console Output

When working correctly, you should see this sequence in the console:

```
🔄 Page saved event received: {sessionId: "123", pageTitle: "New Page"}
🔄 Adding small delay before refreshing page list to ensure API consistency
🔄 Refreshing page list after page save
🔄 Calling getPageList API for project: 456
✅ Page list refreshed, found pages: 3
📄 Page list data: [{id: 121, title: "Page 1"}, {id: 122, title: "Page 2"}, {id: 123, title: "New Page"}]
📄 projectPages state updated: {pageCount: 3, pages: [...], currentSessionId: "123"}
🔄 Auto-selecting newly created page: New Page
✅ New page auto-selection completed: {pageId: "123", pageTitle: "New Page", ...}
```

## Testing Steps

### Test 1: Create New Page
1. Open browser console
2. Create a new page using "New Page" button
3. Enter a prompt and generate
4. Watch console for the debug sequence above
5. **Expected**: New page should appear in sidebar and be selected

### Test 2: Verify Page List Updates
1. Note current page count in sidebar
2. Create a new page
3. **Expected**: Page count should increase by 1
4. **Expected**: New page should be visible in the list

### Test 3: Check API Response
1. Open Network tab in browser dev tools
2. Create a new page
3. Look for `getPageList` API call after page creation
4. **Expected**: Response should include the newly created page

## Troubleshooting

### If pages still don't appear:
1. Check console for API errors in `getPageList` call
2. Verify the `pageSaved` event is being fired
3. Check if `projectId` is correctly set
4. Verify the API response includes the new page

### If pages appear but aren't selected:
1. Check `currentSessionId` state in console logs
2. Verify `handleProjectPageSelect` is being called
3. Check for type mismatches between string/number IDs

### If multiple event handlers fire:
1. Check for duplicate event listener registration
2. Verify dependency array only includes `projectId`
3. Look for race conditions between different handlers

## Key Changes Made

### EditorPageV3Refactored.tsx
1. **refreshProjectPages**: Added useCallback and detailed logging
2. **handlePageSaved**: Reordered operations and added delay
3. **Event listeners**: Fixed dependency array
4. **Debug tracking**: Added projectPages state monitoring

### Expected Behavior
- New pages appear immediately in sidebar after creation
- Page selection works correctly with visual highlighting
- No duplicate event handler registrations
- Stable references prevent unnecessary re-renders
- API timing issues are handled with appropriate delays

## Performance Considerations
- Event listeners are only re-registered when projectId changes
- useCallback prevents unnecessary function recreations
- Debugging logs can be removed in production
- 500ms delay is minimal but ensures API consistency
