import React from "react";
import styles from "./LandingPage.module.css";
import { useNavigate } from "react-router-dom";
import { useBilling } from '../contexts/BillingContext';
import { ContactModal } from '../components/ContactModal';

function useIsLoggedIn() {
  const [isLoggedIn, setIsLoggedIn] = React.useState(false);

  React.useEffect(() => {
    fetch(`${import.meta.env.VITE_API_BASE_URL || '/api'}/auth/me`, {
      credentials: "include",
    })
      .then(async (res) => {
        setIsLoggedIn(res.ok);
      })
      .catch(() => setIsLoggedIn(false));
  }, []);

  return isLoggedIn;
}

export const LandingPage: React.FC = () => {
  const navigate = useNavigate();
  const isLoggedIn = useIsLoggedIn();
  const { plan } = useBilling();
  const isPaid = plan && plan !== 'Free';
  const [contactModalOpen, setContactModalOpen] = React.useState(false);

  const handleStartFreeTrial = (e: React.MouseEvent) => {
    e.preventDefault();
    if (isLoggedIn) {
      navigate("/prompt");
    } else {
      const base = import.meta.env.VITE_API_BASE_URL || "/api";
      window.location.href = `${base}/auth/google?redirect=/prompt`;
    }
  };

  return (
    <div className={styles.landingRoot}>
      {/* HERO SECTION */}
      <section className={styles.heroSection + ' ' + styles.heroGradient + ' animate-fadein'} style={{position:"unset"}}>
        <div className={styles.heroContent}>
          <h1 className={styles.heroHeadline + ' animate-slideup'}>
            Instantly turn your <span className={styles.heroHighlight}>UI Ideas</span> into working prototypes.
          </h1>
          <p className={styles.heroSubheading + ' animate-fadein'}>
            No setup. No code. Just describe your vision and see it come to life.
          </p>
          
          <div className={styles.heroCtas + ' animate-fadein'}>
            {/* Show Start Free Trial for logged-in free users only */}
            {isLoggedIn && !isPaid && (
              <a
                href="/new"
                className={styles.primaryCta}
                onClick={handleStartFreeTrial}
              >
                Start Free Trial
              </a>
            )}
            {/* Show Go to App for paid users */}
            {isLoggedIn && isPaid && (
              <button
                className={styles.primaryCta}
                onClick={() => navigate('/prototypes')}
              >
                Go to App
              </button>
            )}
            <button className={styles.secondaryCta} onClick={() => setContactModalOpen(true)}>
              Feedback & Support
            </button>
          </div>
        </div>
      </section>

      {/* FEATURES SECTION */}
      <section className={styles.featuresSection + ' animate-fadein'} id="features" >
        <div className={styles.featuresGrid}>
          <div className={styles.featureCol + ' ' + styles.featureCard}>
            <div className={styles.featureIconCircle + ' ' + styles.iconFast} aria-hidden="true">🔥</div>
            <div className={styles.featureTitle}>Fast</div>
            <div className={styles.featureDesc}>Instant HTML from your idea</div>
          </div>
          <div className={styles.featureCol + ' ' + styles.featureCard}>
            <div className={styles.featureIconCircle + ' ' + styles.iconClean} aria-hidden="true">🧼</div>
            <div className={styles.featureTitle}>Clean</div>
            <div className={styles.featureDesc}>No JS errors or clutter</div>
          </div>
          <div className={styles.featureCol + ' ' + styles.featureCard}>
            <div className={styles.featureIconCircle + ' ' + styles.iconSimple} aria-hidden="true">🧠</div>
            <div className={styles.featureTitle}>Simple</div>
            <div className={styles.featureDesc}>Visual editing & element selector</div>
          </div>
        </div>
      </section>

      {/* HOW IT WORKS SECTION */}
      <section className={styles.howItWorksSection + ' animate-fadein'}>
        <div className={styles.howItWorksTitle}>How It Works</div>
        <div className={styles.howSteps}>
          <div className={styles.howStep}>
            <div className={styles.howStepIcon}>💡</div>
            <div className={styles.howStepText}>Describe your UI idea</div>
          </div>
          <div className={styles.howStep}>
            <div className={styles.howStepIcon}>⚡</div>
            <div className={styles.howStepText}>Get instant HTML5 prototype</div>
          </div>
          <div className={styles.howStep}>
            <div className={styles.howStepIcon}>🎨</div>
            <div className={styles.howStepText}>Edit visually & share</div>
          </div>
        </div>
      </section>

      <ContactModal isOpen={contactModalOpen} onClose={() => setContactModalOpen(false)} />
    </div>
  );
};

export default LandingPage;
