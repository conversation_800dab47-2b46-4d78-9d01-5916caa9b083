// ActionRegistry.ts
// SPA Action Registry - Handles all data-action triggers in the SPA

export class ActionRegistry {
  private actions: Record<string, Function>;

  constructor() {
    this.actions = {};
    this.registerDefaultActions();
  }

  registerDefaultActions() {
    // Modal Actions
    this.actions['openModal'] = (targetId: string, params: any, el: HTMLElement) => {
      // Prevent modal opening in edit mode
      if (window && (window as any).spaCore?.isEditMode) {
        console.log('🔒 Modal opening prevented in edit mode');
        return;
      }
      const modal = document.getElementById(targetId);
      if (modal) {
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
        // Add smooth transition
        modal.style.opacity = '0';
        modal.style.transition = 'opacity 0.3s ease';
        setTimeout(() => modal.style.opacity = '1', 10);
        console.log('✅ Modal opened:', targetId);
      }
    };

    this.actions['closeModal'] = (targetId: string, params: any, el: HTMLElement) => {
      const modal = document.getElementById(targetId);
      if (modal) {
        modal.style.opacity = '0';
        setTimeout(() => {
          modal.style.display = 'none';
          document.body.style.overflow = 'auto';
          console.log('✅ Modal closed:', targetId);
        }, 300);
      }
    };

    // Chart Actions
    this.actions['initChart'] = (targetId: string, params: any, el: HTMLElement) => {
      const chartId = el.getAttribute('data-chart-id');
      const chartType = el.getAttribute('data-chart-type') || 'line';
      const chartData = JSON.parse(el.getAttribute('data-chart-data') || '[]');
      const chartLabels = JSON.parse(el.getAttribute('data-chart-labels') || '[]');

      if (chartId && (window as any).echarts) {
        const chartContainer = document.getElementById(chartId);
        if (chartContainer) {
          const chart = (window as any).echarts.init(chartContainer);
          const option = {
            title: { text: el.getAttribute('data-chart-title') || 'Chart' },
            tooltip: { trigger: 'axis' },
            xAxis: { type: 'category', data: chartLabels },
            yAxis: { type: 'value' },
            series: [{
              type: chartType,
              data: chartData,
              smooth: chartType === 'line',
              itemStyle: { color: '#3b82f6' }
            }]
          };
          chart.setOption(option);

          // Make chart responsive
          window.addEventListener('resize', () => chart.resize());
          console.log('📊 Chart initialized:', chartId);
        }
      }
    };

    // Form Actions
    this.actions['submitForm'] = (targetId: string, params: any, el: HTMLElement) => {
      console.log('📝 Form submitted:', targetId, params);
      // Add form submission logic here
    };

    // Navigation Actions (handled by router)
    this.actions['navigate'] = (targetId: string, params: any, el: HTMLElement) => {
      const viewName = el.getAttribute('data-nav');
      if (viewName && (window as any).spaCore?.router) {
        (window as any).spaCore.router.navigateToView(viewName);
      }
    };

    // Generic filter action that works with any data-filter attribute
    this.actions['filter'] = (targetId: string, params: any, el: HTMLElement) => {
      const filter = el.getAttribute('data-filter');
      const filterGroup = el.getAttribute('data-filter-group') || 'default';
      console.log('🔍 Filtering:', filter, 'in group:', filterGroup);
      
      // Update active filter button styling within the same group
      const filterButtons = document.querySelectorAll(`[data-action="filter"][data-filter-group="${filterGroup}"]`);
      filterButtons.forEach(btn => {
        btn.classList.remove('bg-blue-600', 'text-white');
        btn.classList.add('bg-white', 'text-gray-700');
      });
      el.classList.remove('bg-white', 'text-gray-700');
      el.classList.add('bg-blue-600', 'text-white');
      
      // Find target elements to filter (look for data-filter-target)
      const targetSelector = el.getAttribute('data-filter-target');
      if (targetSelector) {
        const targetElements = document.querySelectorAll(targetSelector);
        targetElements.forEach(row => {
          if (filter === 'all') {
            (row as HTMLElement).style.display = '';
          } else {
            // Generic filtering: look for elements with data-filter-value matching the filter
            const filterValue = row.getAttribute('data-filter-value');
            const shouldShow = !filterValue || filterValue === filter || filterValue.includes(filter);
            (row as HTMLElement).style.display = shouldShow ? '' : 'none';
          }
        });
      }
    };

    // Generic toggle action for any element
    this.actions['toggle'] = (targetId: string, params: any, el: HTMLElement) => {
      const targetElement = targetId ? document.getElementById(targetId) : el;
      const toggleClass = el.getAttribute('data-toggle-class') || 'hidden';
      
      if (targetElement) {
        targetElement.classList.toggle(toggleClass);
        console.log('🔄 Toggled class:', toggleClass, 'on element:', targetElement.id);
      }
    };

    // Generic update action to change text/content
    this.actions['update'] = (targetId: string, params: any, el: HTMLElement) => {
      const targetElement = targetId ? document.getElementById(targetId) : null;
      const newText = el.getAttribute('data-update-text');
      const newClass = el.getAttribute('data-update-class');
      
      if (targetElement && newText) {
        targetElement.textContent = newText;
      }
      if (targetElement && newClass) {
        targetElement.className = newClass;
      }
      console.log('📝 Updated element:', targetElement?.id);
    };

    // Generic chart rendering that works with any chart data
    this.actions['renderChart'] = (targetId: string, params: any, el: HTMLElement) => {
      const chartType = el.getAttribute('data-chart-type');
      const chartData = el.getAttribute('data-chart-data');
      const chartLabels = el.getAttribute('data-chart-labels');
      const chartTitle = el.getAttribute('data-chart-title');
      
      console.log('📊 Rendering generic chart:', chartType);
      
      if ((window as any).echarts && chartData && chartLabels) {
        try {
          const data = JSON.parse(chartData);
          const labels = JSON.parse(chartLabels);
          
          const chart = (window as any).echarts.init(el);
          
          let option: any = {
            title: { text: chartTitle || 'Chart', left: 'center' },
            tooltip: { trigger: chartType === 'pie' ? 'item' : 'axis' }
          };
          
          if (chartType === 'pie') {
            option.series = [{
              type: 'pie',
              radius: '50%',
              data: labels.map((label: string, i: number) => ({ name: label, value: data[i] })),
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }];
          } else {
            option.xAxis = { type: 'category', data: labels };
            option.yAxis = { type: 'value' };
            option.series = [{
              type: chartType || 'bar',
              data: data,
              smooth: chartType === 'line',
              itemStyle: { color: '#3b82f6' }
            }];
          }
          
          chart.setOption(option);
          window.addEventListener('resize', () => chart.resize());
        } catch (error) {
          console.error('Chart data parsing error:', error);
        }
      }
    };

    // Generic button actions
    this.actions['showAlert'] = (targetId: string, params: any, el: HTMLElement) => {
      const message = el.getAttribute('data-message') || 'Action completed!';
      alert(message);
    };

    // ESC key handler for modals
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        const openModals = document.querySelectorAll('[id$="Modal"][style*="flex"]');
        openModals.forEach(modal => {
          this.actions['closeModal'](modal.id, {}, modal as HTMLElement);
        });
      }
    });

    console.log('🎯 Generic actions registered: openModal, closeModal, initChart, submitForm, navigate, showAlert, filter, toggle, update, renderChart');
  }

  execute(action: string, target: string | null, params: any, el: HTMLElement) {
    if (this.actions[action]) {
      this.actions[action](target, params, el);
    }
  }

  // Allow registering custom actions
  register(action: string, handler: Function) {
    this.actions[action] = handler;
  }

  clear() {
    this.actions = {};
  }
}

export default ActionRegistry;
