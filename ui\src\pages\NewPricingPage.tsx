import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { PricingModule, PricingPlan } from "../modules/pricing";
import { useBilling } from "../contexts/BillingContext";
import { ContactModal } from "../components/ContactModal";

/**
 * Simplified pricing page focusing only on the Free version
 */
export const NewPricingPage: React.FC = () => {
  const { plan: currentPlan } = useBilling();
  const [contactModalOpen, setContactModalOpen] = useState(false);
  const navigate = useNavigate();

  // Define pricing plans with correct strategy
  const plans: PricingPlan[] = [
    {
      id: "free",
      name: "Free",
      description: "Perfect for getting started",
      price: 0,
      buttonText: "Get Started",
      features: [
        { name: "3 prototypes per month" },
        { name: "Advanced design options" },
        { name: "HTML5/CSS output" },
        { name: "Community support" }
      ],
      quota: {
        prototypeLimit: 3
      },
      onSelect: () => {
        navigate('/myprototype');
      }
    },
    {
      id: "pro",
      name: "Pro",
      description: "For growing teams",
      price: 10,
      buttonText: "Coming Soon",
      disabled: true,
      features: [
        { name: "30 prototypes per month" },
        { name: "Advanced design options" },
        { name: "Clean HTML5/CSS output" },
        { name: "Priority support" },
        { name: "Export to Figma" }
      ],
      quota: {
        prototypeLimit: 30
      }
    },
    {
      id: "enterprise",
      name: "Enterprise",
      description: "Self-hosting & custom solutions",
      price: 0, // No price shown - contact us for pricing
      buttonText: "Contact Us",
      isFeatured: true,
      badge: "Self-Hosting",
      features: [
        { name: "Self-hosting deployment" },
        { name: "Unlimited prototypes" },
        { name: "Custom LLM integration" },
        { name: "Priority support" },
        { name: "Custom branding" },
        { name: "SSO integration" }
      ],
      onSelect: () => {
        setContactModalOpen(true);
      }
    }
  ];

  // Custom header to explain the pricing model
  const customHeader = (
    <div style={{ textAlign: 'center', maxWidth: '800px', margin: '0 auto', padding: '2rem 1rem' }}>
      <h1 style={{ fontSize: '2.5rem', fontWeight: 'bold', marginBottom: '1rem' }}>Simple, Transparent Pricing</h1>
      <p style={{ fontSize: '1.25rem', color: '#4b5563', marginBottom: '2rem' }}>
        Start free, upgrade when you need more. Enterprise plans include self-hosting for complete data privacy.
      </p>
    </div>
  );

  return (
    <>
      <PricingModule
        plans={plans}
        currentPlanId={currentPlan === "Free" ? "free" : "pro"}
        customHeader={customHeader}
        // Remove faqItems, comparisonData, and yearlyDiscountPercentage
      />
      <ContactModal
        isOpen={contactModalOpen}
        onClose={() => setContactModalOpen(false)}
      />
    </>
  );
};

export default NewPricingPage;
