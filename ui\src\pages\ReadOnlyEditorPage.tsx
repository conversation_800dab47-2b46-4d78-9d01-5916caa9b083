/**
 * Read-Only Editor Page - For viewing shared prototypes
 * Reuses existing editor services and components with editing disabled
 */

import React, { useEffect, useState, useRef } from 'react';
import { useParams } from 'react-router-dom';
import { FiLoader } from 'react-icons/fi';
import { SPAShell } from '../components/SPAShell';
import { useEditorV3 } from '../hooks/useEditorV3';

interface SharedPrototype {
  id: string;
  title: string;
  html: string;
  description?: string;
  createdAt: string;
  ownerName?: string;
}

export function ReadOnlyEditorPage() {
  const { hash } = useParams<{ hash: string }>();
  const [prototype, setPrototype] = useState<SharedPrototype | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const spaShellRef = useRef<HTMLDivElement>(null);

  console.log('🔥 ReadOnlyEditorPage loaded with hash:', hash);

  // Use the existing editor hook but in read-only mode
  const { state } = useEditorV3({
    projectId: undefined,
    sessionId: undefined
  });

  useEffect(() => {
    console.log('🔗 ReadOnlyEditorPage mounted with hash:', hash);

    if (!hash) {
      console.log('🔗 No hash provided, showing error');
      setError('Invalid share link');
      setLoading(false);
      return;
    }

    const fetchSharedPrototype = async () => {
      try {
        setLoading(true);
        setError(null);

        const API_BASE = import.meta.env.VITE_API_BASE_URL || '/api';
        const url = `${API_BASE}/share/access/${hash}`;

        console.log('🔗 Fetching shared prototype:', {
          hash,
          API_BASE,
          url,
          env: import.meta.env.VITE_API_BASE_URL
        });

        const response = await fetch(url);

        console.log('🔗 Response status:', response.status);

        if (!response.ok) {
          const errorText = await response.text();
          console.log('🔗 Error response body:', errorText);

          if (response.status === 404) {
            throw new Error('Shared prototype not found or link has expired');
          }
          throw new Error(`Failed to load shared prototype: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log('🔗 Shared prototype data:', data);

        setPrototype(data.prototype || data);
      } catch (err: any) {
        console.error('🔗 Error fetching shared prototype:', err);
        setError(err.message || 'Failed to load shared prototype');
      } finally {
        setLoading(false);
      }
    };

    fetchSharedPrototype();
  }, [hash]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <FiLoader className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading shared prototype...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <FiShare2 className="w-8 h-8 text-red-600" />
          </div>
          <h1 className="text-xl font-semibold text-gray-900 mb-2">Unable to Load Prototype</h1>
          <p className="text-gray-600 mb-4">{error}</p>
          <a
            href="/"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <FiExternalLink className="w-4 h-4 mr-2" />
            Go to JustPrototype
          </a>
        </div>
      </div>
    );
  }

  if (!prototype) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">No prototype data found</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-white">
      {/* Full Screen Content - Maximum Real Estate */}
      <div className="h-full overflow-hidden">
        <SPAShell
          ref={spaShellRef}
          className="h-full"
          enableEditMode={false} // Disable edit mode for read-only
          dashboardHtml={prototype.html || state.htmlContent || state.stableIframeContent}
          streamingContent=""
          isGenerating={false}
          onElementClick={undefined} // No element clicking in read-only mode
          viewMode="preview"
          useSPAMode={true}
        />
      </div>
    </div>
  );
}

export default ReadOnlyEditorPage;
