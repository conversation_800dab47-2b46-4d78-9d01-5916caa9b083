# Chart Rendering Fix - January 21, 2025

## Problem
Charts in generated prototypes are not rendering despite having correct data attributes and ECharts library loaded.

## Root Cause
**Structural Mismatch**: LLM generates combined chart container + trigger elements, but SPAShell expects them separated.

### Current Generated Structure (BROKEN):
```html
<div id="completion-chart" class="h-64" data-action="initChart" data-chart-type="line" 
     data-chart-data="[65, 59, 80, 81, 56, 55, 40]" 
     data-chart-labels="['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']"></div>
```

### Required Structure (WORKING):
```html
<div id="completion-chart" class="h-64"></div>
<div data-action="initChart" data-chart-id="completion-chart" data-chart-type="line" 
     data-chart-data="[65, 59, 80, 81, 56, 55, 40]" 
     data-chart-labels="['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']"></div>
```

## Key Issue
- Missing `data-chart-id` attribute that SPAShell uses to find target container
- Chart container and trigger element must be separate elements

## Solution
Update prompt engineering in `backend/config/prompts.js` to generate correct HTML structure.

## Files to Fix
- `backend/config/prompts.js` - Lines 586-634 (Chart implementation section)

## Implementation Status
- [x] Problem identified
- [x] Prompt engineering updated
- [x] GenericSPA class updated with timing fixes
- [x] Manual debug2.html fix verified working
- [x] **CRITICAL FIX**: Fixed generation logic in codeGeneration.fromScratch prompt
- [ ] Testing with new generated prototypes

## Additional Issues Found in debug2.html
1. **Missing Chart Data**: Charts had no data-chart-data or data-chart-labels
2. **ID Mismatch**: Container IDs didn't match data-chart-id values
3. **Missing JavaScript**: No GenericSPA class or event handlers at all
4. **Incomplete Prompt Implementation**: LLM still not following updated prompts

## Changes Made
1. **Prompt Updates** in `backend/config/prompts.js`:
   - Lines 586-606: Fixed main chart implementation section
   - Lines 624-637: Updated chart data examples
   - Line 238: Updated chart requirements summary
   - Lines 1095-1115: Replaced Chart.js with ECharts pattern

2. **Manual Fix** in `backend/debug/debug2.html`:
   - Fixed chart structure with proper data attributes
   - Added complete GenericSPA JavaScript initialization
   - Fixed chart container/ID matching
   - Added realistic chart data

## Key Changes
- Emphasized separate container and trigger elements
- Added `data-chart-id` requirement
- Removed Chart.js references, standardized on ECharts
- Updated all examples to show correct structure
- **CRITICAL**: LLM must include JavaScript initialization code

## Final Fixes Applied

### 3. **GenericSPA Class Updates** in `backend/config/prompts.js`:
   - Lines 787-848: Updated initChart method with visibility checking
   - Lines 898-944: Enhanced initializeViewCharts with deferred chart handling
   - Lines 639-648: Added mandatory chart requirements

### 4. **Key Improvements**:
   - **Timing Fix**: Charts now handle hidden containers correctly
   - **Deferred Initialization**: Charts initialize when tabs become visible
   - **Error Prevention**: Proper visibility checks before ECharts init
   - **Mandatory Requirements**: Clear instructions for LLM chart generation

### 5. **PROPER ARCHITECTURE FIX** - SPAShell JavaScript Injection:
   - **Root Cause**: JavaScript should be injected by SPAShell, not generated by LLM
   - **SPAShell Fix**: Added `injectGenericSPAScript()` function in `ui/src/components/SPAShell.tsx`
   - **Lines 400-498**: Complete GenericSPA script auto-injection with chart support
   - **Prompt Cleanup**: Removed JavaScript requirements from prompts - SPAShell handles it
   - **Architecture**: LLM generates HTML structure, SPAShell provides functionality

## Expected Results
- ✅ Charts render correctly in Analytics/Reports tabs
- ✅ Tab navigation works smoothly
- ✅ No more "charts appear only when dev tools open" issue
- ✅ All future generated prototypes should work correctly
- ✅ **NEW PROTOTYPES WILL INCLUDE COMPLETE JAVASCRIPT AUTOMATICALLY**
