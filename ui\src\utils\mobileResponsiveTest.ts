/**
 * Mobile Responsive Test Utilities
 * 
 * This module provides utilities to test and verify the mobile-responsive
 * behavior of the editor interface.
 */

export interface ResponsiveTestResult {
  testName: string;
  passed: boolean;
  details: string;
  element?: HTMLElement | null;
}

export class MobileResponsiveTest {
  
  /**
   * Run all mobile responsive tests
   */
  static runAllTests(): ResponsiveTestResult[] {
    const results: ResponsiveTestResult[] = [];
    
    console.log('🧪 Running Mobile Responsive Tests...');
    
    // Test 1: Header toggle button exists
    results.push(this.testHeaderToggleExists());
    
    // Test 2: Mobile tabs exist on mobile viewport
    results.push(this.testMobileTabsExist());
    
    // Test 3: Touch target sizes
    results.push(this.testTouchTargetSizes());
    
    // Test 4: Responsive layout switching
    results.push(this.testResponsiveLayoutSwitching());
    
    // Test 5: Mobile content visibility
    results.push(this.testMobileContentVisibility());
    
    // Test 6: Desktop layout preservation
    results.push(this.testDesktopLayoutPreservation());
    
    // Display results
    this.displayTestResults(results);
    
    return results;
  }
  
  /**
   * Test if header toggle button exists and is accessible
   */
  static testHeaderToggleExists(): ResponsiveTestResult {
    const toggleButton = document.querySelector('[class*="toggleButton"]') as HTMLElement;
    
    if (!toggleButton) {
      return {
        testName: 'Header Toggle Button',
        passed: false,
        details: 'Toggle button not found in header',
        element: null
      };
    }
    
    const styles = window.getComputedStyle(toggleButton);
    const minHeight = parseInt(styles.minHeight);
    const minWidth = parseInt(styles.minWidth);
    
    const isAccessible = minHeight >= 44 && minWidth >= 44;
    
    return {
      testName: 'Header Toggle Button',
      passed: isAccessible,
      details: `Toggle button found with size ${minWidth}x${minHeight}px (${isAccessible ? 'PASS' : 'FAIL'}: needs ≥44px)`,
      element: toggleButton
    };
  }
  
  /**
   * Test if mobile tabs exist and are properly configured
   */
  static testMobileTabsExist(): ResponsiveTestResult {
    const mobileTabNav = document.querySelector('[class*="mobileTabNav"]') as HTMLElement;
    
    if (!mobileTabNav) {
      return {
        testName: 'Mobile Tab Navigation',
        passed: false,
        details: 'Mobile tab navigation not found',
        element: null
      };
    }
    
    const tabs = mobileTabNav.querySelectorAll('[class*="mobileTab"]');
    const hasPreviewTab = Array.from(tabs).some(tab => tab.textContent?.includes('Preview'));
    const hasChatTab = Array.from(tabs).some(tab => tab.textContent?.includes('Chat'));
    
    const passed = tabs.length === 2 && hasPreviewTab && hasChatTab;
    
    return {
      testName: 'Mobile Tab Navigation',
      passed,
      details: `Found ${tabs.length} tabs (Preview: ${hasPreviewTab}, Chat: ${hasChatTab})`,
      element: mobileTabNav
    };
  }
  
  /**
   * Test touch target sizes for mobile accessibility
   */
  static testTouchTargetSizes(): ResponsiveTestResult {
    const interactiveElements = document.querySelectorAll('button, [role="button"], [class*="mobileTab"]');
    let failedElements = 0;
    let totalElements = interactiveElements.length;
    
    interactiveElements.forEach(element => {
      const styles = window.getComputedStyle(element as HTMLElement);
      const height = parseInt(styles.height) || parseInt(styles.minHeight);
      const width = parseInt(styles.width) || parseInt(styles.minWidth);
      
      if (height < 44 || width < 44) {
        failedElements++;
        console.warn(`Touch target too small: ${element.className} (${width}x${height}px)`);
      }
    });
    
    const passed = failedElements === 0;
    
    return {
      testName: 'Touch Target Sizes',
      passed,
      details: `${totalElements - failedElements}/${totalElements} elements meet 44px minimum (${failedElements} failed)`,
      element: null
    };
  }
  
  /**
   * Test responsive layout switching between mobile and desktop
   */
  static testResponsiveLayoutSwitching(): ResponsiveTestResult {
    const isMobile = window.innerWidth < 768;
    const mobileContent = document.querySelector('[class*="mobileContentArea"]');
    const desktopLayout = document.querySelector('[class*="desktopLayout"]');
    
    let passed = false;
    let details = '';
    
    if (isMobile) {
      passed = !!mobileContent && !desktopLayout;
      details = `Mobile viewport (${window.innerWidth}px): Mobile content ${mobileContent ? 'found' : 'missing'}, Desktop layout ${desktopLayout ? 'incorrectly visible' : 'hidden'}`;
    } else {
      passed = !mobileContent && !!desktopLayout;
      details = `Desktop viewport (${window.innerWidth}px): Desktop layout ${desktopLayout ? 'found' : 'missing'}, Mobile content ${mobileContent ? 'incorrectly visible' : 'hidden'}`;
    }
    
    return {
      testName: 'Responsive Layout Switching',
      passed,
      details,
      element: (mobileContent || desktopLayout) as HTMLElement
    };
  }
  
  /**
   * Test mobile content visibility and tab switching
   */
  static testMobileContentVisibility(): ResponsiveTestResult {
    const isMobile = window.innerWidth < 768;
    
    if (!isMobile) {
      return {
        testName: 'Mobile Content Visibility',
        passed: true,
        details: 'Skipped - not in mobile viewport',
        element: null
      };
    }
    
    const previewTab = document.querySelector('[class*="mobilePreviewTab"]');
    const chatTab = document.querySelector('[class*="mobileChatTab"]');
    
    // Check if only one tab is visible at a time
    const previewVisible = previewTab && window.getComputedStyle(previewTab as HTMLElement).display !== 'none';
    const chatVisible = chatTab && window.getComputedStyle(chatTab as HTMLElement).display !== 'none';
    
    const passed = (previewVisible && !chatVisible) || (!previewVisible && chatVisible);
    
    return {
      testName: 'Mobile Content Visibility',
      passed,
      details: `Preview tab: ${previewVisible ? 'visible' : 'hidden'}, Chat tab: ${chatVisible ? 'visible' : 'hidden'} (only one should be visible)`,
      element: (previewTab || chatTab) as HTMLElement
    };
  }
  
  /**
   * Test desktop layout preservation
   */
  static testDesktopLayoutPreservation(): ResponsiveTestResult {
    const isDesktop = window.innerWidth >= 768;
    
    if (!isDesktop) {
      return {
        testName: 'Desktop Layout Preservation',
        passed: true,
        details: 'Skipped - not in desktop viewport',
        element: null
      };
    }
    
    const sidebar = document.querySelector('[class*="PageSidebar"]');
    const centerPanel = document.querySelector('[class*="flex-1"][class*="flex-col"][class*="bg-gray-50"]');
    const rightPanel = document.querySelector('[class*="w-96"][class*="bg-white"][class*="border-l"]');
    
    const passed = !!sidebar && !!centerPanel && !!rightPanel;
    
    return {
      testName: 'Desktop Layout Preservation',
      passed,
      details: `Three-pane layout: Sidebar ${sidebar ? 'found' : 'missing'}, Center ${centerPanel ? 'found' : 'missing'}, Right ${rightPanel ? 'found' : 'missing'}`,
      element: null
    };
  }
  
  /**
   * Display test results in console
   */
  static displayTestResults(results: ResponsiveTestResult[]): void {
    console.log('\n' + '='.repeat(60));
    console.log('📱 MOBILE RESPONSIVE TEST RESULTS');
    console.log('='.repeat(60));
    
    const passed = results.filter(r => r.passed).length;
    const total = results.length;
    
    results.forEach((result, index) => {
      const status = result.passed ? '✅ PASS' : '❌ FAIL';
      console.log(`${index + 1}. ${result.testName}: ${status}`);
      console.log(`   ${result.details}`);
      
      if (result.element) {
        console.log(`   Element:`, result.element);
      }
      console.log('');
    });
    
    console.log('-'.repeat(60));
    console.log(`📊 SUMMARY: ${passed}/${total} tests passed (${((passed/total)*100).toFixed(1)}%)`);
    
    if (passed === total) {
      console.log('🎉 All tests passed! Mobile responsive implementation is working correctly.');
    } else {
      console.log('⚠️  Some tests failed. Check the details above for issues to fix.');
    }
    
    console.log('='.repeat(60));
  }
  
  /**
   * Simulate mobile viewport for testing
   */
  static simulateMobileViewport(): void {
    // This would typically be done through browser dev tools
    // But we can trigger a resize event to test responsive behavior
    const originalWidth = window.innerWidth;
    
    // Simulate mobile width
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 375
    });
    
    // Trigger resize event
    window.dispatchEvent(new Event('resize'));
    
    console.log('📱 Simulated mobile viewport (375px width)');
    
    // Restore original width after test
    setTimeout(() => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: originalWidth
      });
      window.dispatchEvent(new Event('resize'));
      console.log(`🖥️  Restored original viewport (${originalWidth}px width)`);
    }, 5000);
  }
  
  /**
   * Test tab switching functionality
   */
  static testTabSwitching(): ResponsiveTestResult {
    const previewTabButton = document.querySelector('[class*="mobileTab"]') as HTMLElement;
    const chatTabButton = document.querySelectorAll('[class*="mobileTab"]')[1] as HTMLElement;
    
    if (!previewTabButton || !chatTabButton) {
      return {
        testName: 'Tab Switching',
        passed: false,
        details: 'Tab buttons not found',
        element: null
      };
    }
    
    // Test clicking tabs
    try {
      previewTabButton.click();
      setTimeout(() => {
        chatTabButton.click();
      }, 100);
      
      return {
        testName: 'Tab Switching',
        passed: true,
        details: 'Tab switching functionality works',
        element: previewTabButton
      };
    } catch (error) {
      return {
        testName: 'Tab Switching',
        passed: false,
        details: `Tab switching failed: ${error}`,
        element: previewTabButton
      };
    }
  }
}

// Export for use in browser console
(window as any).MobileResponsiveTest = MobileResponsiveTest;

// Auto-run tests when in development mode
if (process.env.NODE_ENV === 'development') {
  // Run tests after page load
  window.addEventListener('load', () => {
    setTimeout(() => {
      console.log('🔧 Auto-running mobile responsive tests...');
      MobileResponsiveTest.runAllTests();
    }, 2000);
  });
}
