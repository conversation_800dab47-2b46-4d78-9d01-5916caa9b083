const express = require('express');
const passport = require('passport');
const router = express.Router();

// Start Google OAuth login
router.get('/google', (req, res, next) => {
  console.log('[Auth Debug] Starting OAuth flow');
  console.log('[Auth Debug] Request headers:', JSON.stringify(req.headers, null, 2));
  console.log('[Auth Debug] Session ID before OAuth:', req.session?.id);
  console.log('[Auth Debug] Environment:', process.env.NODE_ENV);
  console.log('[Auth Debug] Frontend URL:', process.env.FRONTEND_URL);

  // Store the redirect parameter in the session
  if (req.query.redirect) {
    req.session.redirectAfterAuth = req.query.redirect;
    console.log('[Auth Debug] Stored redirect:', req.query.redirect);
  }

  passport.authenticate('google', {
    scope: ['profile', 'email'],
    prompt: 'select_account'
  })(req, res, next);
});

// Google OAuth callback
router.get('/google/callback',
  (req, res, next) => {
    console.log('[Auth Debug] OAuth callback URL hit');
    console.log('[Auth Debug] Query params:', req.query);

    passport.authenticate('google', {
      failureRedirect: '/',
      session: true,
    })(req, res, (err) => {
      if (err) {
        console.error('[Auth Debug] Passport authentication error:', err);
        return res.redirect('/?error=auth_failed');
      }
      next();
    });
  },
  (req, res) => {
    // Log authentication success
    console.log('[Auth Debug] Google auth callback received');
    console.log('[Auth Debug] Request headers:', JSON.stringify(req.headers, null, 2));
    console.log('[Auth Debug] Google auth successful, user:', req.user?.id);
    console.log('[Auth Debug] Session ID after auth:', req.session?.id);
    console.log('[Auth Debug] Session data:', JSON.stringify(req.session, null, 2));

    // Add a timestamp to the session to ensure it's modified
    req.session.loginTime = Date.now();

    // Force touch the session
    req.session.touch();

    // Save the session explicitly
    req.session.save((err) => {
      if (err) {
        console.error('[Auth Debug] Error saving session:', err);
      }

      // Set authentication cookies that work cross-domain
      const userData = {
        id: req.user.id,
        dbId: req.user.dbId,
        email: req.user.email,
        displayName: req.user.displayName
      };

      // Set a simple auth flag cookie
      res.cookie('isLoggedIn', 'true', {
        maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
        httpOnly: false, // Allow JavaScript access
        path: '/',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        secure: process.env.NODE_ENV === 'production',
        domain: process.env.NODE_ENV === 'production' ? '.justprototype.dev' : undefined
      });

      // Set user data cookie (non-httpOnly so frontend can read it)
      res.cookie('userData', JSON.stringify(userData), {
        maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
        httpOnly: false, // Allow JavaScript access
        path: '/',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        secure: process.env.NODE_ENV === 'production',
        domain: process.env.NODE_ENV === 'production' ? '.justprototype.dev' : undefined
      });

      console.log('[Auth Debug] Auth cookies set with user data:', userData);

      // Redirect to the requested page or default to prototypes
      console.log('[Auth Debug] Redirecting after successful login');
      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
      const redirectPath = req.session.redirectAfterAuth || '/prototypes';

      // Clean up the redirect from session
      delete req.session.redirectAfterAuth;

      // Ensure we don't have double slashes in the URL
      const redirectUrl = frontendUrl.endsWith('/')
        ? `${frontendUrl}${redirectPath.startsWith('/') ? redirectPath.slice(1) : redirectPath}`
        : `${frontendUrl}${redirectPath.startsWith('/') ? redirectPath : '/' + redirectPath}`;

      console.log('[Auth Debug] Redirect URL:', redirectUrl);
      res.redirect(redirectUrl);
    });
  }
);

// Logout
router.get('/logout', (req, res) => {
  console.log('[Auth Debug] Logging out user:', req.user?.id);

  // Set CORS headers directly to ensure cross-origin requests work
  res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Cookie, X-Requested-With, Accept');
  res.header('Access-Control-Expose-Headers', 'Set-Cookie, X-Auth-Status');

  req.logout((err) => {
    if (err) {
      console.error('[Auth Debug] Error during logout:', err);
      return res.status(500).json({ error: 'Logout failed' });
    }

    // Destroy the session
    req.session.destroy((err) => {
      if (err) {
        console.error('[Auth Debug] Error destroying session:', err);
      }

      // Clear all cookies with the same settings they were set with
      res.clearCookie('connect.sid', {
        path: '/',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        secure: process.env.NODE_ENV === 'production',
        httpOnly: true
      });

      res.clearCookie('isLoggedIn', {
        path: '/',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        secure: process.env.NODE_ENV === 'production',
        httpOnly: false
      });

      // Also set an expired cookie to ensure it's cleared
      res.cookie('isLoggedIn', 'false', {
        expires: new Date(0),
        path: '/',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        secure: process.env.NODE_ENV === 'production'
      });

      console.log('[Auth Debug] User logged out successfully');

      // Add a small delay to ensure cookies are properly cleared before redirect
      setTimeout(() => {
        const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';

        // Remove any trailing slash to ensure consistent URLs
        const cleanUrl = frontendUrl.endsWith('/')
          ? frontendUrl.slice(0, -1)
          : frontendUrl;

        console.log('[Auth Debug] Logout redirect URL:', cleanUrl);
        res.redirect(cleanUrl);
      }, 100);
    });
  });
});

// API version of logout that returns JSON instead of redirecting
router.post('/logout', (req, res) => {
  console.log('[Auth Debug] API Logout called for user:', req.user?.id);

  // Set CORS headers directly to ensure cross-origin requests work
  res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Cookie, X-Requested-With, Accept');
  res.header('Access-Control-Expose-Headers', 'Set-Cookie, X-Auth-Status');

  req.logout((err) => {
    if (err) {
      console.error('[Auth Debug] Error during API logout:', err);
      return res.status(500).json({ success: false, error: 'Logout failed' });
    }

    // Destroy the session
    req.session.destroy((err) => {
      if (err) {
        console.error('[Auth Debug] Error destroying session during API logout:', err);
      }

      // Clear all cookies with the same settings they were set with
      res.clearCookie('connect.sid', {
        path: '/',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        secure: process.env.NODE_ENV === 'production',
        httpOnly: true
      });

      res.clearCookie('isLoggedIn', {
        path: '/',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        secure: process.env.NODE_ENV === 'production',
        httpOnly: false
      });

      // Also set an expired cookie to ensure it's cleared
      res.cookie('isLoggedIn', 'false', {
        expires: new Date(0),
        path: '/',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        secure: process.env.NODE_ENV === 'production'
      });

      console.log('[Auth Debug] API Logout successful');
      res.json({ success: true, message: 'Logged out successfully' });
    });
  });
});

// Handle OPTIONS requests for CORS preflight
router.options('/me', (_req, res) => {
  console.log('[Auth Debug] /me OPTIONS request received');
  res.status(204).end();
});

// Handle OPTIONS requests for logout
router.options('/logout', (req, res) => {
  console.log('[Auth Debug] /logout OPTIONS request received');
  res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Cookie, X-Requested-With, Accept');
  res.status(204).end();
});

// Get current user
router.get('/me', (req, res) => {
  console.log('[Auth Debug] /me endpoint called');
  console.log('[Auth Debug] Session ID:', req.session?.id);
  console.log('[Auth Debug] Session data:', JSON.stringify(req.session, null, 2));
  console.log('[Auth Debug] isAuthenticated:', req.isAuthenticated?.());
  console.log('[Auth Debug] req.user:', JSON.stringify(req.user, null, 2));
  console.log('[Auth Debug] Passport session:', req.session?.passport);
  //console.log('[Auth Debug] Cookies:', req.headers.cookie);
  console.log('[Auth Debug] Origin:', req.headers.origin);
  console.log('[Auth Debug] Referer:', req.headers.referer);

  // Set CORS headers directly
  res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Cookie, X-Requested-With, Accept');
  res.header('Access-Control-Expose-Headers', 'Set-Cookie, X-Auth-Status');

  // Force save the session to ensure it's stored
  req.session.touch();
  req.session.save((err) => {
    if (err) {
      console.error('[Auth Debug] Error saving session:', err);
    }

    if (req.user) {
      console.log('[Auth Debug] User authenticated via session:', req.user.id);

      // Set a custom header to indicate authentication status
      res.setHeader('X-Auth-Status', 'authenticated');

      // Return the user data
      res.json({
        user: req.user,
        sessionId: req.session.id,
        authenticated: true,
        authMethod: 'session'
      });
    } else {
      // Fallback: Check for userData cookie
      const userDataCookie = req.cookies?.userData;
      const isLoggedInCookie = req.cookies?.isLoggedIn;

      console.log('[Auth Debug] Session auth failed, checking cookies');
      console.log('[Auth Debug] isLoggedIn cookie:', isLoggedInCookie);
      console.log('[Auth Debug] userData cookie exists:', !!userDataCookie);

      if (isLoggedInCookie === 'true' && userDataCookie) {
        try {
          const userData = JSON.parse(userDataCookie);
          console.log('[Auth Debug] User authenticated via cookie:', userData.id);

          res.setHeader('X-Auth-Status', 'authenticated');
          res.json({
            user: userData,
            sessionId: req.session.id,
            authenticated: true,
            authMethod: 'cookie'
          });
          return;
        } catch (error) {
          console.error('[Auth Debug] Error parsing userData cookie:', error);
        }
      }

      console.log('[Auth Debug] User not authenticated - checking session for passport data');
      console.log('[Auth Debug] Session passport user:', req.session?.passport?.user);

      res.status(401).json({
        error: 'Not authenticated',
        debug: {
          sessionId: req.session?.id,
          hasSession: !!req.session,
          passportUser: req.session?.passport?.user,
          cookies: req.headers.cookie,
          isLoggedInCookie,
          hasUserDataCookie: !!userDataCookie
        }
      });
    }
  });
});

// Debug endpoint to check session state
router.get('/debug', (req, res) => {
  console.log('[Auth Debug] Debug endpoint called');
  console.log('[Auth Debug] Headers:', JSON.stringify(req.headers, null, 2));
  console.log('[Auth Debug] Session:', JSON.stringify(req.session, null, 2));
  console.log('[Auth Debug] User:', JSON.stringify(req.user, null, 2));
  console.log('[Auth Debug] isAuthenticated:', req.isAuthenticated?.());

  res.json({
    sessionId: req.session?.id,
    sessionData: req.session,
    user: req.user,
    isAuthenticated: req.isAuthenticated?.(),
    cookies: req.headers.cookie,
    environment: process.env.NODE_ENV,
    frontendUrl: process.env.FRONTEND_URL
  });
});

module.exports = router;
