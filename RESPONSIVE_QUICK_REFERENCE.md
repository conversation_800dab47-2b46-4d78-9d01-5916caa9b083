# 📱 Responsive Design Quick Reference

## 🚀 Quick Start

### Update All Existing Pages
```bash
# Check current status
node scripts/updateResponsiveDesign.js --stats

# Preview changes
node scripts/updateResponsiveDesign.js --dry-run

# Apply updates
node scripts/updateResponsiveDesign.js
```

### Test Implementation
```bash
# Run comprehensive tests
node scripts/testResponsiveDesign.js
```

## 📐 Breakpoints

| Device | Width | CSS Variable |
|--------|-------|--------------|
| Mobile | 320px+ | `--breakpoint-sm: 640px` |
| Tablet | 768px+ | `--breakpoint-md: 768px` |
| Desktop | 1024px+ | `--breakpoint-lg: 1024px` |
| Large | 1280px+ | `--breakpoint-xl: 1280px` |
| XL | 1536px+ | `--breakpoint-2xl: 1536px` |

## 🎨 Design Tokens

### Typography
```css
font-size: var(--text-xs);    /* clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem) */
font-size: var(--text-sm);    /* clamp(0.875rem, 0.8rem + 0.375vw, 1rem) */
font-size: var(--text-base);  /* clamp(1rem, 0.9rem + 0.5vw, 1.125rem) */
font-size: var(--text-lg);    /* clamp(1.125rem, 1rem + 0.625vw, 1.25rem) */
font-size: var(--text-xl);    /* clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem) */
```

### Spacing
```css
margin: var(--space-xs);   /* clamp(0.25rem, 0.2rem + 0.25vw, 0.375rem) */
margin: var(--space-sm);   /* clamp(0.5rem, 0.4rem + 0.5vw, 0.75rem) */
margin: var(--space-md);   /* clamp(1rem, 0.8rem + 1vw, 1.5rem) */
margin: var(--space-lg);   /* clamp(1.5rem, 1.2rem + 1.5vw, 2.25rem) */
margin: var(--space-xl);   /* clamp(2rem, 1.6rem + 2vw, 3rem) */
```

### Touch Targets
```css
min-height: var(--touch-target-min);         /* 44px */
min-height: var(--touch-target-comfortable); /* 48px */
min-height: var(--touch-target-large);       /* 56px */
```

## 🔧 Common Patterns

### Responsive Container
```css
.container {
  width: 100%;
  margin: 0 auto;
  padding: var(--space-md);
  max-width: var(--container-lg);
}
```

### Responsive Grid
```css
.grid {
  display: grid;
  gap: var(--space-md);
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .grid { grid-template-columns: repeat(2, 1fr); }
}

@media (min-width: 768px) {
  .grid { grid-template-columns: repeat(3, 1fr); }
}
```

### Responsive Flex
```css
.flex-container {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

@media (min-width: 768px) {
  .flex-container {
    flex-direction: row;
  }
}
```

### Touch-Friendly Button
```css
.button {
  min-height: var(--touch-target-min);
  min-width: var(--touch-target-min);
  padding: var(--space-sm) var(--space-md);
  border-radius: 8px;
  transition: all var(--transition-normal);
  touch-action: manipulation;
}
```

## 📱 Mobile Navigation

### HTML Structure
```html
<!-- Mobile Nav Toggle -->
<button id="mobileNavToggle" class="md:hidden">☰</button>

<!-- Mobile Nav Overlay -->
<div id="mobileNavOverlay" class="hidden fixed inset-0 bg-black bg-opacity-50 z-40"></div>

<!-- Mobile Nav Drawer -->
<nav id="mobileNav" class="fixed top-0 left-0 h-full w-80 bg-white z-50 transform -translate-x-full">
  <div id="mobileNavContent">
    <!-- Navigation content -->
  </div>
</nav>
```

### JavaScript Controls
```typescript
import { MobileNavUtils } from './utils/mobileUtils';

// Open/close navigation
MobileNavUtils.openMobileNav();
MobileNavUtils.closeMobileNav();
MobileNavUtils.toggleMobileNav();

// Check state
const isOpen = MobileNavUtils.isMobileNavOpen();
```

## 👆 Touch Gestures

### Setup Touch Handler
```typescript
import { TouchGestureHandler } from './utils/mobileUtils';

const element = document.getElementById('myElement');
const handler = new TouchGestureHandler(element);

handler.setOnSwipe((event) => {
  console.log(`Swiped ${event.direction}`);
});

handler.setOnTap(() => {
  console.log('Tapped');
});

handler.setOnLongPress(() => {
  console.log('Long pressed');
});
```

### React Integration
```tsx
import MobileOptimizedWrapper from './components/MobileOptimizedWrapper';

function MyComponent() {
  return (
    <MobileOptimizedWrapper
      enableSwipeGestures={true}
      onSwipe={(event) => handleSwipe(event)}
    >
      <div>Content</div>
    </MobileOptimizedWrapper>
  );
}
```

## 🎯 Accessibility

### Focus Indicators
```css
*:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}
```

### Reduced Motion
```css
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    transition-duration: 0.01ms !important;
  }
}
```

### High Contrast
```css
@media (prefers-contrast: high) {
  :root {
    --color-primary: #0000ff;
    --color-neutral-800: #000000;
  }
}
```

## 🔍 Device Detection

```typescript
import { DeviceUtils } from './utils/mobileUtils';

// Check device type
const isMobile = DeviceUtils.isMobile();      // < 768px
const isTablet = DeviceUtils.isTablet();      // 768px - 1023px
const isDesktop = DeviceUtils.isDesktop();    // >= 1024px

// Check capabilities
const hasTouch = DeviceUtils.isTouchDevice();
const isLandscape = DeviceUtils.isLandscape();

// Get viewport
const { width, height } = DeviceUtils.getViewportSize();
```

## 🚨 Common Issues & Solutions

### Issue: Text too small on mobile
```css
/* ❌ Fixed size */
font-size: 14px;

/* ✅ Responsive size */
font-size: var(--text-base);
```

### Issue: Touch targets too small
```css
/* ❌ Small target */
button {
  padding: 4px 8px;
}

/* ✅ Touch-friendly */
button {
  min-height: var(--touch-target-min);
  padding: var(--space-sm) var(--space-md);
}
```

### Issue: Horizontal scrolling
```css
/* ❌ Fixed width */
.container {
  width: 1200px;
}

/* ✅ Responsive width */
.container {
  width: 100%;
  max-width: var(--container-lg);
}
```

### Issue: Images not responsive
```css
/* ❌ Fixed size */
img {
  width: 500px;
  height: 300px;
}

/* ✅ Responsive */
img {
  max-width: 100%;
  height: auto;
}
```

## 📊 Testing Checklist

### Mobile (< 768px)
- [ ] Navigation drawer works
- [ ] Touch targets ≥ 44px
- [ ] Text readable without zoom
- [ ] No horizontal scroll
- [ ] Forms usable
- [ ] Swipe gestures work

### Tablet (768px - 1023px)
- [ ] Layout adapts
- [ ] Navigation horizontal
- [ ] Content flows well
- [ ] Touch still works

### Desktop (≥ 1024px)
- [ ] Full layout
- [ ] Hover states
- [ ] Mobile nav hidden
- [ ] Optimal spacing

### Accessibility
- [ ] Keyboard navigation
- [ ] Screen reader friendly
- [ ] Focus indicators
- [ ] Color contrast
- [ ] Reduced motion

## 🔧 API Endpoints

```bash
# Get statistics
curl -X GET /api/responsive-update/stats

# Preview updates
curl -X POST /api/responsive-update/preview \
  -H "Content-Type: application/json" \
  -d '{"batchSize": 50}'

# Execute updates
curl -X POST /api/responsive-update/execute \
  -H "Content-Type: application/json" \
  -d '{"confirm": true, "batchSize": 50}'
```

## 🎨 Tailwind Integration

The responsive framework works alongside Tailwind CSS:

```html
<!-- Combine framework variables with Tailwind -->
<div class="p-4 md:p-8" style="padding: var(--space-md);">
  <h1 class="text-2xl md:text-4xl" style="font-size: var(--text-3xl);">
    Responsive Heading
  </h1>
</div>
```

## 📱 PWA Considerations

```html
<!-- Viewport meta tag -->
<meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">

<!-- Theme color -->
<meta name="theme-color" content="#3b82f6">

<!-- Safe area support -->
<style>
  .safe-area {
    padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
  }
</style>
```

---

*Quick Reference v1.0 - January 2025*
